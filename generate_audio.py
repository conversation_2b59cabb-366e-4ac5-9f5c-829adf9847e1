from gtts import gTTS
import os

# 确保音频目录存在
os.makedirs("assets/audio", exist_ok=True)

# 配音文本
narrations = {
    "intro": "空中拦截行动开始",
    "hq9be": "巴基斯坦红旗9BE防空系统准备就绪",
    "rafale": "印度阵风战机进攻巴基斯坦",
    "radar_detect": "红旗9BE雷达发现阵风战机",
    "info_transfer": "红旗9BE雷达将阵风飞机的信息传给ZDK-03预警机",
    "zdk03": "ZDK-03预警机接收信息",
    "j10ce": "ZDK-03预警机将信息传给歼10CE战机",
    "pl15e": "歼10CE战机接受命令后，发射霹雳15E导弹",
    "missile_guidance": "霹雳15E导弹在预警机的实时引导下追踪",
    "missile_radar": "霹雳15E导弹打开火控雷达",
    "hit": "导弹最终击中阵风战机",
    "mission_complete": "任务完成"
}

# 生成配音文件
for key, text in narrations.items():
    tts = gTTS(text=text, lang='zh-cn', slow=False)
    filename = f"assets/audio/{key}.mp3"
    tts.save(filename)
    print(f"已生成配音: {filename}")

print("所有配音文件生成完成！")
