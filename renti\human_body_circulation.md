# 人体五脏资源传输动画提示词（极简版）

## 动画目标
用manim制作一个极简示意动画，展示五脏（心、肝、脾、肺、肾）通过通道向全身传递不同种类资源的过程。

## 动画元素

1. **人体轮廓（可选）**
   - 可以只用简单的椭圆或线条勾勒人体外形，或直接省略人体，仅保留五脏和通道。

2. **五脏示意**
   - 用五个不同颜色的圆形/椭圆分别代表心、肝、脾、肺、肾，分布在画面中央。
   - 每个器官旁可加文字标签。

3. **资源通道**
   - 从每个器官出发，画出若干条简单的直线/曲线通道，指向人体外围（可用圆环或虚线圈表示“全身”）。
   - 不需要细分血管、淋巴等，只需通道即可。

4. **资源粒子**
   - 用不同颜色的小圆点代表不同资源（如红色代表氧气，黄色代表营养，蓝色代表废物等）。
   - 粒子从五脏出发，沿通道向外运动，抵达外围。
   - 资源种类可自定义，数量不限。

## 动画流程

1. 所有五脏静止出现。
2. 依次或同时，从五脏沿通道发射不同颜色的资源粒子，粒子移动到外围。
3. 可循环展示不同资源类型的传输。
4. （可选）外围出现“全身”字样，粒子到达后消失或闪烁。

## 代码结构建议

```python
from manim import *

class SimpleOrganResourceFlow(Scene):
    def construct(self):
        # 1. 创建五个器官（圆形）
        organs = [Circle(radius=0.3, color=color).shift(pos)
                  for color, pos in zip([RED, GREEN, YELLOW, BLUE, PURPLE],
                                       [UP, RIGHT, LEFT, DOWN, ORIGIN])]
        labels = [Text(name, font_size=24).next_to(org, DOWN)
                  for name, org in zip(["心脏", "肝脏", "脾脏", "肺", "肾脏"], organs)]
        self.add(*organs, *labels)

        # 2. 创建通道（直线/曲线）
        channels = [Line(org.get_center(), 2*org.get_center(), color=WHITE, stroke_width=2)
                    for org in organs]
        self.add(*channels)

        # 3. 资源粒子动画
        for i, org in enumerate(organs):
            dot = Dot(org.get_center(), color=org.get_color())
            self.add(dot)
            self.play(dot.animate.move_to(2*org.get_center()), run_time=1)
            self.remove(dot)

        # 4. 可循环展示不同资源
        # ...
```

## 备注
- 不需要画详细的人体和器官形状。
- 不需要展示血管、血液、淋巴等细节。
- 只需表现“有通道、有资源传出”的极简动态。
- 资源种类、颜色、数量均可自定义。

## 执行命令

```sh
manim -pql renti/human_body_circulation.py SimpleOrganResourceFlow
```