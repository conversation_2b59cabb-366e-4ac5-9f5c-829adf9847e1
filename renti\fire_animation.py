from manim import *

class FireAnimation(Scene):
    def construct(self):
        # 导入 fire.svg 文件
        fire = SVGMobject("fire.svg")
        
        # 设置初始状态
        fire.scale(2)  # 放大火焰
        fire.set_color(RED)  # 设置初始颜色为红色
        
        # 创建火焰出现的动画
        self.play(FadeIn(fire))
        self.wait(0.5)
        
        # 火焰颜色变化动画
        self.play(fire.animate.set_color(YELLOW), run_time=1)
        self.wait(0.5)
        self.play(fire.animate.set_color(ORANGE), run_time=1)
        self.wait(0.5)
        
        # 火焰跳动效果
        for _ in range(3):
            self.play(
                fire.animate.scale(1.2).shift(UP * 0.2),
                run_time=0.3
            )
            self.play(
                fire.animate.scale(1/1.2).shift(DOWN * 0.2),
                run_time=0.3
            )
        
        # 火焰旋转效果
        self.play(Rotate(fire, angle=TAU, run_time=2))
        self.wait(0.5)
        
        # 火焰分解效果
        fire_parts = fire.submobjects.copy() if len(fire.submobjects) > 0 else [fire]
        
        if len(fire_parts) > 1:
            # 如果 SVG 有多个部分，分别处理
            self.remove(fire)
            self.add(*fire_parts)
            
            # 让火焰各部分向不同方向散开
            directions = [UP+LEFT, UP, UP+RIGHT, LEFT, RIGHT, DOWN+LEFT, DOWN, DOWN+RIGHT]
            for i, part in enumerate(fire_parts):
                direction = directions[i % len(directions)]
                self.play(
                    part.animate.shift(direction).scale(0.5).fade(1),
                    run_time=0.5
                )
        else:
            # 如果 SVG 只有一个部分，创建一个消失效果
            self.play(
                fire.animate.scale(1.5).set_opacity(0),
                run_time=1.5
            )
        
        self.wait(1)
