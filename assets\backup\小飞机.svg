<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- 定义渐变和滤镜 -->
  <defs>
    <!-- 战斗机主体渐变 -->
    <linearGradient id="jetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1a73e8" />
      <stop offset="100%" stop-color="#0d47a1" />
    </linearGradient>
    
    <!-- 尾焰渐变 -->
    <linearGradient id="flameGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#ff9800" />
      <stop offset="50%" stop-color="#f44336" />
      <stop offset="100%" stop-color="#790e8b" />
    </linearGradient>
    
    <!-- 阴影效果 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="5" flood-color="#0004" />
    </filter>
  </defs>
  
  <!-- 背景光晕 -->
  <circle cx="100" cy="100" r="80" fill="#1a73e810" />
  
  <!-- 战斗机主体 -->
  <g filter="url(#shadow)">
    <!-- 机身 -->
    <path d="M100,40 L120,80 L115,80 L115,130 L105,140 L95,140 L85,130 L85,80 L80,80 Z" fill="url(#jetGradient)" />
    
    <!-- 机翼 -->
    <path d="M80,80 L60,100 L55,95 L85,80 Z" fill="url(#jetGradient)" />
    <path d="M120,80 L140,100 L145,95 L115,80 Z" fill="url(#jetGradient)" />
    
    <!-- 尾翼 -->
    <path d="M95,140 L100,160 L105,140 Z" fill="url(#jetGradient)" />
    
    <!-- 驾驶舱 -->
    <ellipse cx="100" cy="65" rx="10" ry="15" fill="#e3f2fd" />
    <ellipse cx="100" cy="65" rx="7" ry="12" fill="#2196f3" />
    
    <!-- 引擎 -->
    <rect x="95" y="130" width="10" height="10" fill="#263238" />
  </g>
  
  <!-- 尾焰动画 -->
  <g id="flame">
    <path d="M95,140 L105,140 L102,160 L98,160 Z" fill="url(#flameGradient)">
      <animate attributeName="d" 
               values="M95,140 L105,140 L102,160 L98,160 Z;
                       M95,140 L105,140 L103,165 L97,165 Z;
                       M95,140 L105,140 L102,160 L98,160 Z" 
               dur="0.3s" 
               repeatCount="indefinite" />
    </path>
  </g>
  
  <!-- 整体飞行动画 -->
  <g>
    <animateTransform attributeName="transform" 
                     type="translate" 
                     values="0,10; 0,-10; 0,10" 
                     dur="2s" 
                     repeatCount="indefinite" />
  </g>
</svg>