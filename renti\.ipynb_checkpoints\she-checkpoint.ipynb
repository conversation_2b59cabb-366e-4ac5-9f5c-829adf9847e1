{"cells": [{"cell_type": "code", "execution_count": null, "id": "f93f4111-416a-44b0-8777-5029eca3dc0d", "metadata": {}, "outputs": [], "source": ["from manim import *\n", "import numpy as np\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}