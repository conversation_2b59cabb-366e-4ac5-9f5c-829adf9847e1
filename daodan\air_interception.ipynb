{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 空中拦截动画\n", "\n", "这个 Notebook 展示了使用 Manim 创建的空中拦截动画。动画展示了一个完整的空中拦截场景，包括阵风战机、红旗16防空系统、空警500预警机和歼10战机等。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "from manim import *\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class AirInterception(Scene):\n", "    def construct(self):\n", "        # 设置背景\n", "        sky = Rectangle(\n", "            width=config.frame_width,\n", "            height=config.frame_height,\n", "            fill_color=\"#87CEEB\",\n", "            fill_opacity=1,\n", "            stroke_width=0\n", "        )\n", "        \n", "        ground = Rectangle(\n", "            width=config.frame_width,\n", "            height=config.frame_height/4,\n", "            fill_color=\"#8B4513\",\n", "            fill_opacity=1,\n", "            stroke_width=0\n", "        ).move_to(DOWN * config.frame_height/3)\n", "        \n", "        self.add(sky, ground)\n", "        \n", "        # 创建字幕函数\n", "        def add_subtitle(text, duration=3):\n", "            subtitle = Text(text, font=\"SimSun\", color=WHITE).scale(0.7)\n", "            subtitle.to_edge(DOW<PERSON>, buff=0.5)\n", "            self.play(FadeIn(subtitle))\n", "            self.wait(duration)\n", "            self.play(FadeOut(subtitle))\n", "        \n", "        # 1. 阵风战机进入场景\n", "        add_subtitle(\"印度阵风战机进入巴勒斯坦领空\", 2)\n", "        \n", "        # 加载阵风战机SVG\n", "        rafale = SVGMobject(\"assets/rafale.svg\", fill_opacity=1, fill_color=RED)\n", "        # 调整大小和角度，机头朝右（飞行方向）\n", "        rafale.scale(0.8).rotate(-PI/2)  # 将机头从朝上旋转为朝右\n", "        rafale.move_to(LEFT * 6 + UP * 2)  # 初始位置\n", "        \n", "        self.play(FadeIn(rafale))\n", "        \n", "        # 阵风战机飞行动画\n", "        self.play(\n", "            rafale.animate.move_to(LEFT * 3 + UP * 2),\n", "            rate_func=linear,\n", "            run_time=3\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["        # 2. 红旗16防空系统发现阵风战机\n", "        add_subtitle(\"巴勒斯坦边境的红旗16防空导弹系统雷达发现了阵风战机\", 2)\n", "        \n", "        # 创建红旗16防空系统 (使用hq9_launcher.svg，因为assets中没有hq16)\n", "        hq16_launcher = SVGMobject(\"assets/hq9_launcher.svg\", fill_opacity=1)\n", "        hq16_launcher.scale(1.2)\n", "        hq16_launcher.move_to(DOWN * 2 + RIGHT * 4)\n", "        \n", "        # 直接使用发射车\n", "        self.play(FadeIn(hq16_launcher))\n", "        \n", "        # 雷达波动画，直接从发射车发出\n", "        radar_waves = VGroup()\n", "        # 获取发射车上方的位置作为雷达波起点\n", "        radar_position = hq16_launcher.get_center() + UP * 0.5  # 从发射车上方发出雷达波\n", "        \n", "        for i in range(1, 6):\n", "            wave = Arc(\n", "                angle=PI/2, \n", "                radius=0.5*i, \n", "                stroke_color=RED, \n", "                stroke_width=2, \n", "                stroke_opacity=1/i\n", "            )\n", "            wave.move_to(radar_position)\n", "            radar_waves.add(wave)\n", "        \n", "        # 显示雷达波\n", "        self.play(\n", "            *[GrowFromCenter(wave) for wave in radar_waves],\n", "            run_time=2\n", "        )\n", "        \n", "        # 雷达锁定动画\n", "        lock_line = DashedLine(\n", "            radar_position,  # 使用之前定义的雷达位置变量\n", "            rafale.get_center(),\n", "            color=RED,\n", "            dash_length=0.2\n", "        )\n", "        \n", "        self.play(Create(lock_line))\n", "        self.wait(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["        # 3. 信息传递给空警500\n", "        add_subtitle(\"红旗16雷达将阵风飞机的信息传给空警500预警机\", 2)\n", "        \n", "        # 创建空警500预警机\n", "        kj500 = SVGMobject(\"assets/kj500.svg\", fill_opacity=1)\n", "        # 调整大小和方向，预警机应该在空中左右巡逻\n", "        kj500.scale(0.7).rotate(PI/2)  # 机头朝左，表示巡逻\n", "        kj500.move_to(RIGHT * 3 + UP * 4)\n", "        \n", "        # 在空警500上添加雷达圆盘\n", "        radar_disk = Circle(radius=0.4, fill_color=LIGHT_GREY, fill_opacity=0.8, stroke_width=1)\n", "        radar_disk.move_to(kj500.get_center())\n", "        kj500.add(radar_disk)\n", "        \n", "        self.play(FadeIn(kj500))\n", "        \n", "        # 信息传递动画\n", "        info_dot = Dot(color=YELLOW)\n", "        info_dot.move_to(radar_position)\n", "        \n", "        self.play(\n", "            info_dot.animate.move_to(kj500.get_center()),\n", "            run_time=2\n", "        )\n", "        \n", "        # 空警500雷达波\n", "        kj_radar_waves = VGroup()\n", "        for i in range(1, 6):\n", "            wave = Circle(\n", "                radius=0.5*i, \n", "                stroke_color=BLUE, \n", "                stroke_width=2, \n", "                stroke_opacity=1/i\n", "            )\n", "            wave.move_to(kj500.get_center())\n", "            kj_radar_waves.add(wave)\n", "        \n", "        self.play(\n", "            *[GrowFromCenter(wave) for wave in kj_radar_waves],\n", "            run_time=2\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["        # 4. 空警500指挥歼10\n", "        add_subtitle(\"空警500预警机将信息传给歼10战机\", 2)\n", "        \n", "        # 创建歼10战机\n", "        j10 = SVGMobject(\"assets/j10.svg\", fill_opacity=1)\n", "        # 调整大小和方向，机头朝向目标（左方）\n", "        # 根据修改后的SVG文件，调整缩放和旋转角度\n", "        j10.scale(0.5)  # 减小缩放比例\n", "        # 根据实际需要选择合适的旋转角度：\n", "        # 如果图片已经朝左，则不需要旋转\n", "        # j10.rotate(0)  # 不旋转\n", "        # 如果图片朝右，需要旋转180度\n", "        j10.rotate(PI/2)  # 旋转90度\n", "        # 如果图片朝上，需要旋转90度\n", "        # j10.rotate(PI/2)  # 旋转90度\n", "        # 如果图片朝下，需要旋转-90度\n", "        # j10.rotate(-PI/2)  # 旋转-90度\n", "        j10.move_to(RIGHT * 5 + UP * 2)\n", "        \n", "        self.play(FadeIn(j10))\n", "        \n", "        # 信息传递动画\n", "        info_dot2 = Dot(color=YELLOW)\n", "        info_dot2.move_to(kj500.get_center())\n", "        \n", "        self.play(\n", "            info_dot2.animate.move_to(j10.get_center()),\n", "            run_time=2\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["        # 5. 歼10发射霹雳15导弹\n", "        add_subtitle(\"歼10战机接受命令后，发射霹雳15导弹\", 2)\n", "        \n", "        # 创建霹雳15导弹\n", "        pl15 = SVGMobject(\"assets/空空导弹.svg\", fill_opacity=1).scale(0.05)\n", "        pl15.rotate(PI)  # 调整方向\n", "        pl15.next_to(j10, LEFT, buff=0.2)\n", "        \n", "        self.play(FadeIn(pl15))\n", "        \n", "        # 导弹发射动画\n", "        self.play(\n", "            pl15.animate.move_to(LEFT * 1 + UP * 2),\n", "            rate_func=rate_functions.ease_out_cubic,\n", "            run_time=2\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["        # 6. 导弹追踪阵风战机\n", "        add_subtitle(\"霜雷15导弹在空警50的指引下飞向阵风战机\", 2)\n", "        \n", "        # 空警指引导弹的数据传输动画\n", "        guidance_line = DashedLine(\n", "            kj500.get_center(),\n", "            pl15.get_center(),\n", "            color=BLUE,\n", "            dash_length=0.1\n", "        )\n", "        \n", "        guidance_dots = VGroup(*[\n", "            Dot(color=BLUE).scale(0.5)\n", "            for _ in range(3)\n", "        ])\n", "        \n", "        for dot in guidance_dots:\n", "            dot.move_to(kj500.get_center())\n", "        \n", "        self.play(Create(guidance_line))\n", "        self.play(\n", "            AnimationGroup(\n", "                *[dot.animate.move_to(pl15.get_center()) for dot in guidance_dots],\n", "                lag_ratio=0.3\n", "            ),\n", "            run_time=1.5\n", "        )\n", "        \n", "        # 导弹开始追踪阵风\n", "        initial_missile_path = Line(\n", "            pl15.get_center(),\n", "            LEFT * 0 + UP * 2,  # 中间点\n", "            stroke_opacity=0\n", "        )\n", "        \n", "        self.play(\n", "            MoveAlongPath(pl15, initial_missile_path),\n", "            run_time=1\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["        # 7. 导弹接近时阵风尝试躲避\n", "        add_subtitle(\"导弹接近时，阵风战机尝试躲避\", 2)\n", "        \n", "        # 阵风向上躲避\n", "        evasion_path = Arc(\n", "            radius=1.5,\n", "            angle=PI/3,\n", "            start_angle=0,\n", "            stroke_opacity=0\n", "        )\n", "        evasion_path.shift(rafale.get_center())\n", "        \n", "        self.play(\n", "            MoveAlongPath(rafale, evasion_path),\n", "            rafale.animate.rotate(-PI/6),  # 机头向上倾斜\n", "            run_time=1.5\n", "        )\n", "        \n", "        # 8. 导弹打开火控雷达追踪\n", "        add_subtitle(\"霜雷15导弹打开火控雷达追踪目标\", 2)\n", "        \n", "        # 导弹雷达波\n", "        missile_radar = VGroup()\n", "        for i in range(1, 4):\n", "            wave = Circle(\n", "                radius=0.2*i, \n", "                stroke_color=RED, \n", "                stroke_width=2, \n", "                stroke_opacity=1/i\n", "            )\n", "            wave.move_to(pl15.get_center())\n", "            missile_radar.add(wave)\n", "        \n", "        self.play(\n", "            *[GrowFromCenter(wave) for wave in missile_radar],\n", "            run_time=1\n", "        )\n", "        \n", "        # 导弹调整路径追踪阵风\n", "        pursuit_path = ArcBetweenPoints(\n", "            pl15.get_center(),\n", "            rafale.get_center(),\n", "            angle=-PI/4  # 向上追踪\n", "        )\n", "        \n", "        self.play(\n", "            MoveAlongPath(pl15, pursuit_path),\n", "            run_time=2\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["        # 9. 导弹接近并击中阵风\n", "        add_subtitle(\"导弹最终追上并击中阵风战机\", 2)\n", "        \n", "        # 导弹接近并爆炸\n", "        self.play(\n", "            pl15.animate.move_to(rafale.get_center()),\n", "            run_time=1\n", "        )\n", "        \n", "        # 爆炸效果\n", "        explosion = Circle(radius=0.1, fill_color=YELLOW, fill_opacity=1, stroke_width=0)\n", "        explosion.move_to(rafale.get_center())\n", "        \n", "        self.play(\n", "            explosion.animate.scale(10).set_opacity(0),\n", "            rafale.animate.set_opacity(0.2),\n", "            run_time=1\n", "        )\n", "        \n", "        # 10. 阵风战机被击落\n", "        add_subtitle(\"阵风战机被击落\", 2)\n", "        \n", "        # 坠落动画\n", "        fall_path = Line(\n", "            rafale.get_center(),\n", "            rafale.get_center() + DOWN * 4 + RIGHT * 2\n", "        )\n", "        \n", "        self.play(\n", "            MoveAlongPath(rafale, fall_path),\n", "            rafale.animate.rotate(PI/2),\n", "            run_time=2\n", "        )\n", "        \n", "        # 结束字幕\n", "        final_text = Text(\"任务完成\", font=\"SimSun\", color=WHITE).scale(1)\n", "        final_text.to_edge(DOWN, buff=1)\n", "        \n", "        self.play(FadeIn(final_text))\n", "        self.wait(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 运行动画\n", "scene = AirInterception()\n", "scene.render()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 注意事项\n", "\n", "1. 运行此 Notebook 需要安装 Manim 库和相关依赖\n", "2. 确保 assets 文件夹中包含所有需要的 SVG 文件：\n", "   - rafale.svg（阵风战机）\n", "   - hq9_launcher.svg（红旗9发射车）\n", "   - kj500.svg（空警500预警机）\n", "   - j10.svg（歼10战机）\n", "   - 空空导弹.svg（霹雳15导弹）\n", "3. 如需调整动画效果，可以修改相应的参数"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}