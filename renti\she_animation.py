from manim import *
import numpy as np

class TongueAnalysisScene(Scene):
    """舌的部位解析和观察维度介绍"""

    def construct(self):
        # 设置背景为白色
        self.camera.background_color = "#FFFFFF"

        # 创建标题
        title = Text("舌部位解析与观察维度", font="SimHei", color=BLACK).scale(1.0)
        title.to_edge(UP, buff=0.3)
        self.play(Write(title))

        # 创建舌头的各个部分 - 调整位置使其更居中
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        tongue_group = VGroup(tongue_body, tongue_coating)
        tongue_group.scale(0.8).move_to(LEFT * 2.5)  # 将舌头移到左侧，为右侧文本留出空间

        # 1. 首先显示舌质
        self.play(FadeIn(tongue_body))

        # 舌质标签和说明 - 改善位置
        tongue_body_label = Text("舌质", font="SimHei", color=BLACK).scale(0.7)
        tongue_body_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(tongue_body_label))

        # 创建右侧文本区域背景
        text_bg = Rectangle(
            width=5.5, height=6,
            fill_color=WHITE, fill_opacity=0.9,
            stroke_color=GRAY, stroke_width=1
        ).move_to(RIGHT * 3.5)
        self.play(FadeIn(text_bg))

        # 舌质说明文本 - 重新设计布局
        tongue_body_text = Text(
            "舌质：舌头的肉质部分\n是舌诊的重要观察对象",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        tongue_body_text.move_to(text_bg.get_center() + UP * 2)

        # 添加从文本到舌质的箭头 - 改善指向
        body_arrow = Arrow(
            start=tongue_body_text.get_left() + LEFT * 0.2,
            end=tongue_body.get_center() + RIGHT * 0.5,
            color=RED,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_body_text), Create(body_arrow))
        self.wait(2)

        # 2. 然后显示舌苔
        self.play(FadeIn(tongue_coating))

        # 舌苔标签和说明
        tongue_coating_label = Text("舌苔", font="SimHei", color=BLACK).scale(0.7)
        tongue_coating_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(FadeOut(tongue_body_label), Write(tongue_coating_label))

        # 舌苔说明文本 - 改善布局
        tongue_coating_text = Text(
            "舌苔：舌头表面的薄薄苔状物\n反映胃肠等消化系统状况",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        tongue_coating_text.move_to(text_bg.get_center() + UP * 2)

        # 添加从文本到舌苔的箭头 - 改善指向
        coating_arrow = Arrow(
            start=tongue_coating_text.get_left() + LEFT * 0.2,
            end=tongue_coating.get_center() + RIGHT * 0.3,
            color=BLUE,
            buff=0.1,
            stroke_width=3
        )

        self.play(FadeOut(tongue_body_text), FadeOut(body_arrow),
                 Write(tongue_coating_text), Create(coating_arrow))
        self.wait(2)

        # 3. 舌质观察维度
        self.play(FadeOut(tongue_coating), FadeOut(tongue_coating_label),
                 FadeOut(tongue_coating_text), FadeOut(coating_arrow))

        # 重新显示舌质标签
        tongue_body_label = Text("舌质观察维度", font="SimHei", color=BLACK).scale(0.7)
        tongue_body_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(tongue_body_label))

        # 添加步骤指示器
        step_indicator = Text("步骤 1/2", font="SimHei", color=GRAY).scale(0.5)
        step_indicator.to_corner(UR, buff=0.5)
        self.play(Write(step_indicator))

        # 舌质观察维度文本 - 改善布局和可读性
        dimensions_text = Text(
            "舌质观察维度：\n\n• 颜色：淡红、淡白、红、紫等\n\n• 润泽度：润泽、干燥等\n\n• 形态：正常、胖大、瘦小等",
            font="SimHei",
            color=BLACK,
            line_spacing=1.0
        ).scale(0.5)
        dimensions_text.move_to(text_bg.get_center())
        self.play(Write(dimensions_text))

        # 颜色维度动画展示 - 改善高亮显示
        color_highlight = Text("→ 颜色变化演示", font="SimHei", color=RED).scale(0.6)
        color_highlight.move_to(text_bg.get_center() + DOWN * 2.5)

        # 创建颜色标签
        color_labels = VGroup(
            Text("正常", font="SimHei", color=BLACK).scale(0.4),
            Text("淡白", font="SimHei", color=BLACK).scale(0.4),
            Text("红色", font="SimHei", color=BLACK).scale(0.4),
            Text("紫色", font="SimHei", color=BLACK).scale(0.4)
        ).arrange(RIGHT, buff=0.5)
        color_labels.next_to(color_highlight, DOWN, buff=0.3)

        self.play(FadeIn(color_highlight), FadeIn(color_labels))

        # 演示不同颜色变化
        colors = [
            rgb_to_color([0.9, 0.5, 0.5]),    # 正常
            rgb_to_color([0.95, 0.85, 0.85]), # 淡白
            rgb_to_color([0.9, 0.3, 0.3]),    # 红色
            rgb_to_color([0.6, 0.3, 0.6])     # 紫色
        ]

        for i, color in enumerate(colors):
            # 高亮当前标签
            if i > 0:
                color_labels[i-1].animate.set_color(BLACK)
            self.play(
                tongue_body.animate.set_color(color),
                color_labels[i].animate.set_color(RED)
            )
            self.wait(1)

        self.play(FadeOut(color_highlight), FadeOut(color_labels))

        # 润泽度维度动画展示 - 改善效果
        moisture_highlight = Text("→ 润泽度变化演示", font="SimHei", color=BLUE).scale(0.6)
        moisture_highlight.move_to(text_bg.get_center() + DOWN * 2.5)

        # 创建润泽度标签
        moisture_labels = VGroup(
            Text("润泽", font="SimHei", color=BLACK).scale(0.4),
            Text("干燥", font="SimHei", color=BLACK).scale(0.4)
        ).arrange(RIGHT, buff=1.0)
        moisture_labels.next_to(moisture_highlight, DOWN, buff=0.3)

        self.play(FadeIn(moisture_highlight), FadeIn(moisture_labels))

        # 创建干燥效果 - 改善视觉效果
        dry_lines = VGroup()
        for i in range(6):
            line = Line(
                start=np.array([-0.6 + 0.2 * i, -0.8, 0]),
                end=np.array([-0.4 + 0.2 * i, 0.8, 0]),
                color=rgb_to_color([0.7, 0.3, 0.3]),
                stroke_width=2
            )
            dry_lines.add(line)
        dry_lines.move_to(tongue_body.get_center())

        # 演示润泽到干燥的变化
        self.play(moisture_labels[0].animate.set_color(BLUE))
        self.wait(1)
        self.play(
            moisture_labels[0].animate.set_color(BLACK),
            moisture_labels[1].animate.set_color(BLUE),
            tongue_body.animate.set_fill(opacity=0.7),
            FadeIn(dry_lines)
        )
        self.wait(1)
        self.play(
            FadeOut(dry_lines),
            tongue_body.animate.set_fill(opacity=1),
            FadeOut(moisture_highlight),
            FadeOut(moisture_labels)
        )

        # 形态维度动画展示 - 改善效果
        shape_highlight = Text("→ 形态变化演示", font="SimHei", color=GREEN).scale(0.6)
        shape_highlight.move_to(text_bg.get_center() + DOWN * 2.5)

        # 创建形态标签
        shape_labels = VGroup(
            Text("正常", font="SimHei", color=BLACK).scale(0.4),
            Text("胖大", font="SimHei", color=BLACK).scale(0.4),
            Text("瘦小", font="SimHei", color=BLACK).scale(0.4)
        ).arrange(RIGHT, buff=0.8)
        shape_labels.next_to(shape_highlight, DOWN, buff=0.3)

        self.play(FadeIn(shape_highlight), FadeIn(shape_labels))

        # 演示形态变化
        self.play(shape_labels[0].animate.set_color(GREEN))
        self.wait(1)

        # 胖大舌
        self.play(
            shape_labels[0].animate.set_color(BLACK),
            shape_labels[1].animate.set_color(GREEN),
            tongue_body.animate.scale(1.3)
        )
        self.wait(1)

        # 瘦小舌
        self.play(
            shape_labels[1].animate.set_color(BLACK),
            shape_labels[2].animate.set_color(GREEN),
            tongue_body.animate.scale(0.54)  # 0.7/1.3 = 0.54
        )
        self.wait(1)

        # 恢复正常
        self.play(
            shape_labels[2].animate.set_color(BLACK),
            shape_labels[0].animate.set_color(GREEN),
            tongue_body.animate.scale(1.85),  # 1/0.54 = 1.85
            FadeOut(shape_highlight),
            FadeOut(shape_labels)
        )

        self.wait(1)
        self.play(FadeOut(dimensions_text))

        # 4. 舌苔观察维度
        self.play(FadeIn(tongue_coating), FadeOut(tongue_body_label))

        # 更新步骤指示器
        step_indicator_2 = Text("步骤 2/2", font="SimHei", color=GRAY).scale(0.5)
        step_indicator_2.to_corner(UR, buff=0.5)
        self.play(FadeOut(step_indicator), Write(step_indicator_2))

        tongue_coating_label = Text("舌苔观察维度", font="SimHei", color=BLACK).scale(0.7)
        tongue_coating_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(tongue_coating_label))

        # 舌苔观察维度文本 - 改善布局
        coating_dimensions_text = Text(
            "舌苔观察维度：\n\n• 颜色：白、黄、灰黑等\n\n• 厚度：薄、厚等\n\n• 质地：腻、燥、剥落等",
            font="SimHei",
            color=BLACK,
            line_spacing=1.0
        ).scale(0.5)
        coating_dimensions_text.move_to(text_bg.get_center())
        self.play(Write(coating_dimensions_text))

        # 舌苔颜色维度动画展示 - 改善效果
        coating_color_highlight = Text("→ 舌苔颜色变化演示", font="SimHei", color=RED).scale(0.6)
        coating_color_highlight.move_to(text_bg.get_center() + DOWN * 2.5)

        # 创建舌苔颜色标签
        coating_color_labels = VGroup(
            Text("白苔", font="SimHei", color=BLACK).scale(0.4),
            Text("黄苔", font="SimHei", color=BLACK).scale(0.4),
            Text("灰黑苔", font="SimHei", color=BLACK).scale(0.4)
        ).arrange(RIGHT, buff=0.7)
        coating_color_labels.next_to(coating_color_highlight, DOWN, buff=0.3)

        self.play(FadeIn(coating_color_highlight), FadeIn(coating_color_labels))

        # 演示舌苔颜色变化
        coating_colors = [
            rgb_to_color([0.95, 0.95, 0.95]),  # 白苔
            rgb_to_color([0.95, 0.9, 0.5]),    # 黄苔
            rgb_to_color([0.3, 0.3, 0.3])      # 灰黑苔
        ]

        for i, color in enumerate(coating_colors):
            if i > 0:
                coating_color_labels[i-1].animate.set_color(BLACK)
            self.play(
                tongue_coating.animate.set_color(color),
                coating_color_labels[i].animate.set_color(RED)
            )
            self.wait(1)

        self.play(FadeOut(coating_color_highlight), FadeOut(coating_color_labels))

        # 厚度维度动画展示 - 改善效果
        thickness_highlight = Text("→ 舌苔厚度变化演示", font="SimHei", color=BLUE).scale(0.6)
        thickness_highlight.move_to(text_bg.get_center() + DOWN * 2.5)

        # 创建厚度标签
        thickness_labels = VGroup(
            Text("薄苔", font="SimHei", color=BLACK).scale(0.4),
            Text("厚苔", font="SimHei", color=BLACK).scale(0.4)
        ).arrange(RIGHT, buff=1.0)
        thickness_labels.next_to(thickness_highlight, DOWN, buff=0.3)

        self.play(FadeIn(thickness_highlight), FadeIn(thickness_labels))

        # 演示厚度变化
        self.play(thickness_labels[0].animate.set_color(BLUE))
        self.wait(1)
        self.play(
            thickness_labels[0].animate.set_color(BLACK),
            thickness_labels[1].animate.set_color(BLUE),
            tongue_coating.animate.set_opacity(0.7)
        )
        self.wait(1)
        self.play(
            thickness_labels[1].animate.set_color(BLACK),
            thickness_labels[0].animate.set_color(BLUE),
            tongue_coating.animate.set_opacity(0.2),
            FadeOut(thickness_highlight),
            FadeOut(thickness_labels)
        )

        # 质地维度动画展示 - 改善效果
        texture_highlight = Text("→ 舌苔质地变化演示", font="SimHei", color=GREEN).scale(0.6)
        texture_highlight.move_to(text_bg.get_center() + DOWN * 2.5)

        # 创建质地标签
        texture_labels = VGroup(
            Text("正常", font="SimHei", color=BLACK).scale(0.4),
            Text("腻苔", font="SimHei", color=BLACK).scale(0.4),
            Text("剥落", font="SimHei", color=BLACK).scale(0.4)
        ).arrange(RIGHT, buff=0.7)
        texture_labels.next_to(texture_highlight, DOWN, buff=0.3)

        self.play(FadeIn(texture_highlight), FadeIn(texture_labels))

        # 创建剥落区域 - 改善位置
        peeling_area = Polygon(
            np.array([-0.3, 0.2, 0]),
            np.array([0.3, 0.2, 0]),
            np.array([0.2, -0.3, 0]),
            np.array([-0.2, -0.3, 0]),
            color=rgb_to_color([0.9, 0.5, 0.5]),
            fill_opacity=1
        )
        peeling_area.move_to(tongue_coating.get_center())

        # 演示质地变化
        self.play(texture_labels[0].animate.set_color(GREEN))
        self.wait(1)

        # 腻苔
        self.play(
            texture_labels[0].animate.set_color(BLACK),
            texture_labels[1].animate.set_color(GREEN),
            tongue_coating.animate.set_opacity(0.5)
        )
        self.wait(1)

        # 剥落苔
        self.play(
            texture_labels[1].animate.set_color(BLACK),
            texture_labels[2].animate.set_color(GREEN),
            tongue_coating.animate.set_opacity(0.3),
            FadeIn(peeling_area)
        )
        self.wait(1)

        # 恢复正常
        self.play(
            FadeOut(peeling_area),
            tongue_coating.animate.set_opacity(0.2),
            FadeOut(texture_highlight),
            FadeOut(texture_labels)
        )

        self.wait(1)
        self.play(FadeOut(coating_dimensions_text))

        # 总结 - 改善布局
        conclusion_text = Text(
            "舌诊是中医诊断的重要方法\n通过观察舌质和舌苔的变化\n可以了解人体内部的健康状况",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.6)
        conclusion_text.move_to(text_bg.get_center())

        self.play(Write(conclusion_text))
        self.wait(3)

        # 淡出所有内容
        self.play(
            FadeOut(tongue_body),
            FadeOut(tongue_coating),
            FadeOut(tongue_coating_label),
            FadeOut(conclusion_text),
            FadeOut(text_bg),
            FadeOut(step_indicator_2),
            FadeOut(title)
        )

    def create_normal_tongue_parts(self):
        """创建正常舌象的各个部分，便于逐步显示"""
        # 简化SVG路径，创建一个更简单的舌头轮廓
        tongue_outline = VMobject(color=BLACK, stroke_width=1.5)

        # 定义舌头轮廓的关键点
        # 根据SVG路径数据，舌头是一个长形，底部宽，顶部稍窄

        # 顶部点（舌根）
        top_left = np.array([-0.8, 0.8, 0])
        top_right = np.array([0.8, 0.8, 0])

        # 底部点（舌尖）
        bottom_left = np.array([-0.6, -1.2, 0])  # 舌尖更长
        bottom_right = np.array([0.6, -1.2, 0])

        # 使用贝塞尔曲线创建平滑的舌头轮廓

        # 左侧曲线 - 从舌根到舌尖的左侧
        left_curve = CubicBezier(
            start_anchor=top_left,
            start_handle=np.array([-1.0, 0.5, 0]),
            end_handle=np.array([-1.2, -0.5, 0]),
            end_anchor=bottom_left
        )

        # 底部曲线 - 舌尖部分
        bottom_curve = CubicBezier(
            start_anchor=bottom_left,
            start_handle=np.array([-0.3, -1.6, 0]),
            end_handle=np.array([0.3, -1.6, 0]),
            end_anchor=bottom_right
        )

        # 右侧曲线 - 从舌尖到舌根的右侧
        right_curve = CubicBezier(
            start_anchor=bottom_right,
            start_handle=np.array([1.2, -0.5, 0]),
            end_handle=np.array([1.0, 0.5, 0]),
            end_anchor=top_right
        )

        # 顶部曲线 - 舌根部分
        top_curve = CubicBezier(
            start_anchor=top_right,
            start_handle=np.array([0.5, 1.0, 0]),
            end_handle=np.array([-0.5, 1.0, 0]),
            end_anchor=top_left
        )

        # 组合所有曲线形成完整的舌头轮廓
        tongue_outline.append_points(left_curve.points)
        tongue_outline.append_points(bottom_curve.points)
        tongue_outline.append_points(right_curve.points)
        tongue_outline.append_points(top_curve.points)
        tongue_outline.close_path()

        # 填充舌体 - 粉红色
        tongue_body = tongue_outline.copy()
        tongue_body.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=1)  # 粉红色

        # 正常舌苔 - 薄白色并覆盖大部分舌面
        tongue_coating = tongue_outline.copy()
        tongue_coating.scale(0.85)  # 稍小一点的舌苔区域
        tongue_coating.set_fill(rgb_to_color([0.95, 0.95, 0.95]), opacity=0.2)  # 薄白色

        # 调整大小和位置
        tongue_body.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小
        tongue_coating.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小

        return tongue_body, tongue_coating

class TongueAnimation(Scene):
    """中医舌诊动画类"""

    def construct(self):
        # 设置背景为白色
        self.camera.background_color = "#FFFFFF"

        # 创建标题
        title = Text("中医察舌", font="SimHei", color=BLACK).scale(1.0)
        title.to_edge(UP, buff=0.3)
        self.play(Write(title))

        # 创建正常舌象的各个部分 - 改善布局
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        tongue_group = VGroup(tongue_body, tongue_coating)
        tongue_group.scale(0.9).move_to(LEFT * 2.5)  # 将舌头移到左侧

        # 创建右侧信息区域背景
        info_bg = Rectangle(
            width=5.5, height=6,
            fill_color=WHITE, fill_opacity=0.9,
            stroke_color=GRAY, stroke_width=1
        ).move_to(RIGHT * 3.5)
        self.play(FadeIn(info_bg))

        # 创建正常舌象标签
        normal_tongue_label = Text("正常舌象", font="SimHei", color=BLACK).scale(0.7)
        normal_tongue_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(normal_tongue_label))

        # 1. 先显示舌质颜色
        self.play(FadeIn(tongue_body))

        # 显示舌质说明文本 - 改善布局
        tongue_color_text = Text(
            "舌质淡红明润",
            font="SimHei",
            color=BLACK
        ).scale(0.6)
        tongue_color_text.move_to(info_bg.get_center() + UP * 2)

        # 添加从文本到舌质的箭头 - 改善指向
        color_arrow = Arrow(
            start=tongue_color_text.get_left() + LEFT * 0.2,
            end=tongue_body.get_center() + RIGHT * 0.5,
            color=RED,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_color_text), Create(color_arrow))
        self.wait(1.5)
        self.play(FadeOut(tongue_color_text), FadeOut(color_arrow))
        self.wait(0.2)

        # 2. 显示舌苔
        self.play(FadeIn(tongue_coating))

        # 显示舌苔说明文本 - 改善布局
        tongue_coating_text = Text(
            "舌苔薄白均匀",
            font="SimHei",
            color=BLACK
        ).scale(0.6)
        tongue_coating_text.move_to(info_bg.get_center() + UP * 2)

        # 添加从文本到舌苔的箭头 - 改善指向
        coating_arrow = Arrow(
            start=tongue_coating_text.get_left() + LEFT * 0.2,
            end=tongue_coating.get_center() + RIGHT * 0.3,
            color=BLUE,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_coating_text), Create(coating_arrow))
        self.wait(1.5)
        self.play(FadeOut(tongue_coating_text), FadeOut(coating_arrow))
        self.wait(0.2)

        # 显示完整说明文本 - 改善布局
        complete_description = Text(
            "正常舌象特征：\n\n• 舌质淡红明润\n• 舌苔薄白均匀\n• 舌体大小适中\n• 边缘光滑无齿痕",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        complete_description.move_to(info_bg.get_center())

        self.play(Write(complete_description))
        self.wait(2)

        # 将所有部分组合成一个完整的舌象
        normal_tongue = VGroup(tongue_body, tongue_coating)

        # 展示异常舌象
        self.show_abnormal_tongues(normal_tongue, complete_description, normal_tongue_label, info_bg)

        # 结束动画
        self.wait(2)



    def create_normal_tongue(self):
        """创建正常舌象，根据SVG路径数据创建"""
        tongue_body, tongue_coating = self.create_normal_tongue_parts()  # 移除舌沟

        # 组合在一起
        normal_tongue = VGroup(tongue_body, tongue_coating)  # 移除舌沟

        # 调整大小和位置
        normal_tongue.scale(1.2)  # 调整整体大小，从0.8改为1.2使舌头更大
        normal_tongue.move_to(ORIGIN)

        return normal_tongue

    def create_normal_tongue_parts(self):
        """创建正常舌象的各个部分，便于逐步显示"""
        # 简化SVG路径，创建一个更简单的舌头轮廓
        tongue_outline = VMobject(color=BLACK, stroke_width=1.5)

        # 定义舌头轮廓的关键点
        # 根据SVG路径数据，舌头是一个长形，底部宽，顶部稍窄

        # 顶部点（舌根）
        top_left = np.array([-0.8, 0.8, 0])
        top_right = np.array([0.8, 0.8, 0])

        # 底部点（舌尖）
        bottom_left = np.array([-0.6, -1.2, 0])  # 舌尖更长
        bottom_right = np.array([0.6, -1.2, 0])

        # 使用贝塞尔曲线创建平滑的舌头轮廓

        # 左侧曲线 - 从舌根到舌尖的左侧
        left_curve = CubicBezier(
            start_anchor=top_left,
            start_handle=np.array([-1.0, 0.5, 0]),
            end_handle=np.array([-1.2, -0.5, 0]),
            end_anchor=bottom_left
        )

        # 底部曲线 - 舌尖部分
        bottom_curve = CubicBezier(
            start_anchor=bottom_left,
            start_handle=np.array([-0.3, -1.6, 0]),
            end_handle=np.array([0.3, -1.6, 0]),
            end_anchor=bottom_right
        )

        # 右侧曲线 - 从舌尖到舌根的右侧
        right_curve = CubicBezier(
            start_anchor=bottom_right,
            start_handle=np.array([1.2, -0.5, 0]),
            end_handle=np.array([1.0, 0.5, 0]),
            end_anchor=top_right
        )

        # 顶部曲线 - 舌根部分
        top_curve = CubicBezier(
            start_anchor=top_right,
            start_handle=np.array([0.5, 1.0, 0]),
            end_handle=np.array([-0.5, 1.0, 0]),
            end_anchor=top_left
        )

        # 组合所有曲线形成完整的舌头轮廓
        tongue_outline.append_points(left_curve.points)
        tongue_outline.append_points(bottom_curve.points)
        tongue_outline.append_points(right_curve.points)
        tongue_outline.append_points(top_curve.points)
        tongue_outline.close_path()

        # 填充舌体 - 粉红色
        tongue_body = tongue_outline.copy()
        tongue_body.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=1)  # 粉红色

        # 移除舌沟的创建

        # 正常舌苔 - 薄白色并覆盖大部分舌面
        tongue_coating = tongue_outline.copy()
        tongue_coating.scale(0.85)  # 稍小一点的舌苔区域
        tongue_coating.set_fill(rgb_to_color([0.95, 0.95, 0.95]), opacity=0.2)  # 薄白色

        # 调整大小和位置
        tongue_body.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小
        tongue_coating.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小

        return tongue_body, tongue_coating  # 移除舌沟

    def show_abnormal_tongues(self, normal_tongue, complete_description, normal_tongue_label, info_bg):
        """展示异常舌象"""
        # 创建分类标题
        category_title = Text("异常舌象分类", font="SimHei", color=BLACK).scale(0.8)
        category_title.to_edge(UP, buff=2)

        # 隐藏正常舌象的描述文本
        self.play(FadeOut(complete_description), FadeOut(normal_tongue_label))

        # 展示舌色异常
        self.show_color_abnormalities(normal_tongue, info_bg)

        # 展示舌形异常
        self.show_shape_abnormalities(normal_tongue, info_bg)

        # 展示舌苔异常
        self.show_coating_abnormalities(normal_tongue, info_bg)

        # 恢复正常舌象的描述文本
        self.play(FadeIn(complete_description), FadeIn(normal_tongue_label))


    def show_color_abnormalities(self, normal_tongue, info_bg):
        """展示舌色异常"""
        # 标题
        title = Text("舌色异常", font="SimHei", color=BLACK).scale(0.8)
        title.to_edge(UP, buff=2.0)

        # 先显示标题
        self.play(Write(title))

        # 分解正常舌象，只取出舌体
        normal_tongue_body = normal_tongue[0].copy()
        normal_tongue_without_coating = normal_tongue_body
        normal_tongue_without_coating.move_to(LEFT * 2.5)  # 保持左侧位置

        # 显示正常舌体（无舌苔）
        self.play(FadeIn(normal_tongue_without_coating))
        self.wait(1)

        # 淡白舌标签
        pale_tongue_label = Text("淡白舌", font="SimHei", color=BLACK).scale(0.6)
        pale_tongue_label.next_to(normal_tongue_without_coating, DOWN, buff=0.8)

        # 淡白舌描述文字 - 改善布局
        pale_tongue_desc = Text(
            "淡白舌：正常舌质淡红变为淡白\n提示气血两虚，阳虚",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        pale_tongue_desc.move_to(info_bg.get_center())

        # 使用颜色渐变展示舌质颜色变化
        self.play(
            normal_tongue_body.animate.set_fill(rgb_to_color([0.95, 0.85, 0.85])),
            Write(pale_tongue_label),
            Write(pale_tongue_desc)
        )

        self.wait(2)

        # 红舌标签
        red_tongue_label = Text("红舌", font="SimHei", color=BLACK).scale(0.6)
        red_tongue_label.next_to(normal_tongue_without_coating, DOWN, buff=0.8)

        # 红舌描述文字 - 改善布局
        red_tongue_desc = Text(
            "红舌：正常舌质淡红变为鲜红\n提示热证",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        red_tongue_desc.move_to(info_bg.get_center())

        # 使用颜色渐变展示从淡白舌变为红舌
        self.play(
            FadeOut(pale_tongue_label),
            FadeOut(pale_tongue_desc),
            normal_tongue_body.animate.set_fill(rgb_to_color([0.9, 0.3, 0.3])),
            Write(red_tongue_label),
            Write(red_tongue_desc)
        )

        self.wait(2)

        # 紫舌标签
        purple_tongue_label = Text("紫舌", font="SimHei", color=BLACK).scale(0.6)
        purple_tongue_label.next_to(normal_tongue_without_coating, DOWN, buff=0.8)

        # 紫舌描述文字 - 改善布局
        purple_tongue_desc = Text(
            "紫舌：正常舌质淡红变为紫色\n提示血瘀、寒凝",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        purple_tongue_desc.move_to(info_bg.get_center())

        # 使用颜色渐变展示从红舌变为紫舌
        self.play(
            FadeOut(red_tongue_label),
            FadeOut(red_tongue_desc),
            normal_tongue_body.animate.set_fill(rgb_to_color([0.6, 0.3, 0.6])),
            Write(purple_tongue_label),
            Write(purple_tongue_desc)
        )

        self.wait(2)

        # 恢复正常舌象
        self.play(
            FadeOut(normal_tongue_without_coating),
            FadeOut(purple_tongue_label),
            FadeOut(purple_tongue_desc),
            FadeOut(title),
            FadeIn(normal_tongue)
        )

    def show_shape_abnormalities(self, normal_tongue, info_bg):
        """展示舌形异常"""
        # 标题
        title = Text("舌形异常", font="SimHei", color=BLACK).scale(0.8)
        title.to_edge(UP, buff=2.0)  # 调整标题位置，防止与主标题重叠

        # 先显示标题
        self.play(Write(title))

        # 创建正常舌象的副本
        normal_tongue_copy = normal_tongue.copy()
        normal_tongue_copy.move_to(ORIGIN)

        # 显示正常舌象
        self.play(FadeIn(normal_tongue_copy))
        self.wait(1)

        # 胖大舌标签
        fat_tongue_label = Text("胖大舌", font="SimHei", color=BLACK).scale(0.6)
        fat_tongue_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头

        # 胖大舌描述
        fat_tongue_desc = Text(
            "胖大舌：正常舌体变得胖大，提示脂肾阳虚",
            font="SimHei",
            color=BLACK
        ).scale(0.5)
        fat_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头  # 调整位置避免重叠

        # 使用缩放动画展示从正常舌象到胖大舌的变化
        self.play(
            normal_tongue_copy.animate.scale(1.3),  # 水平和垂直方向都放大
            Write(fat_tongue_label),
            Write(fat_tongue_desc)
        )

        self.wait(2)

        # 齿痕舌标签
        teeth_mark_tongue_label = Text("齿痕舌", font="SimHei", color=BLACK).scale(0.6)
        teeth_mark_tongue_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头

        # 齿痕舌描述
        teeth_mark_tongue_desc = Text(
            "齿痕舌：正常舌体边缘出现齿痕，提示脂虚湿盛",
            font="SimHei",
            color=BLACK
        ).scale(0.5)
        teeth_mark_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头

        # 创建齿痕
        left_mark1 = Line(LEFT * 0.6 + UP * 0.2, LEFT * 0.6 + DOWN * 0.2, color=BLACK)
        left_mark2 = Line(LEFT * 0.8 + UP * 0.15, LEFT * 0.8 + DOWN * 0.15, color=BLACK)
        right_mark1 = Line(RIGHT * 0.6 + UP * 0.2, RIGHT * 0.6 + DOWN * 0.2, color=BLACK)
        right_mark2 = Line(RIGHT * 0.8 + UP * 0.15, RIGHT * 0.8 + DOWN * 0.15, color=BLACK)
        teeth_marks = VGroup(left_mark1, left_mark2, right_mark1, right_mark2)
        teeth_marks.scale(1.3)  # 与胖大舌的缩放比例一致

        # 动画展示：添加齿痕
        self.play(
            FadeOut(fat_tongue_label),
            FadeOut(fat_tongue_desc),
            FadeIn(teeth_marks),
            Write(teeth_mark_tongue_label),
            Write(teeth_mark_tongue_desc)
        )

        self.wait(2)

        # 裂纹舌标签
        cracked_tongue_label = Text("裂纹舌", font="SimHei", color=BLACK).scale(0.6)
        cracked_tongue_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头

        # 裂纹舌描述
        cracked_tongue_desc = Text(
            "裂纹舌：正常舌面出现裂纹，提示阴血亡虚",
            font="SimHei",
            color=BLACK
        ).scale(0.5)
        cracked_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头

        # 创建裂纹
        center_crack = Line(DOWN * 0.3, UP * 0.3, color=BLACK)
        left_crack = Line(LEFT * 0.4 + DOWN * 0.1, LEFT * 0.4 + UP * 0.2, color=BLACK).rotate(PI/6)
        right_crack = Line(RIGHT * 0.4 + DOWN * 0.1, RIGHT * 0.4 + UP * 0.2, color=BLACK).rotate(-PI/6)
        cracks = VGroup(center_crack, left_crack, right_crack)
        cracks.scale(1.3)  # 与胖大舌的缩放比例一致

        # 动画展示：替换齿痕为裂纹
        self.play(
            FadeOut(teeth_marks),
            FadeOut(teeth_mark_tongue_label),
            FadeOut(teeth_mark_tongue_desc),
            FadeIn(cracks),
            Write(cracked_tongue_label),
            Write(cracked_tongue_desc)
        )

        self.wait(2)

        # 恢复正常舌象
        self.play(
            FadeOut(normal_tongue_copy),
            FadeOut(cracks),
            FadeOut(cracked_tongue_label),
            FadeOut(cracked_tongue_desc),
            FadeOut(title),
            FadeIn(normal_tongue)
        )

    def show_coating_abnormalities(self, normal_tongue, info_bg):
        """展示舌苔异常"""
        # 标题
        title = Text("舌苔异常", font="SimHei", color=BLACK).scale(0.8)
        title.to_edge(UP, buff=2.0)  # 调整标题位置，防止与主标题重叠

        # 先显示标题
        self.play(Write(title))

        # 分解正常舌象，取出舌体和舌苔，不包含舌沟
        normal_tongue_copy = normal_tongue.copy()
        normal_tongue_body = normal_tongue_copy[0]  # 舌体
        normal_tongue_coating = normal_tongue_copy[1]  # 舌苔

        # 组合在一起
        normal_tongue_copy.move_to(ORIGIN)

        # 显示正常舌象
        self.play(FadeIn(normal_tongue_copy))
        self.wait(1)

        # 厚腻苔标签
        thick_coating_label = Text("厚腻苔", font="SimHei", color=BLACK).scale(0.6)
        thick_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头

        # 厚腻苔描述
        thick_coating_desc = Text(
            "厚腻苔：正常舌苔变得厚腻，提示痰湿、食积",
            font="SimHei",
            color=BLACK
        ).scale(0.5)
        thick_coating_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头  # 调整位置避免重叠

        # 创建厚腻苔（将厚度增加，不改变舌体颜色）
        thick_coating = normal_tongue_coating.copy()
        thick_coating.set_fill(rgb_to_color([0.9, 0.9, 0.9]), opacity=0.7)  # 增加不透明度
        thick_coating.scale(1.2)  # 增加舌苔大小

        # 动画展示：正常舌苔变为厚腻苔
        self.play(
            FadeOut(normal_tongue_coating),
            FadeIn(thick_coating),
            Write(thick_coating_label),
            Write(thick_coating_desc)
        )

        self.wait(2)

        # 黄苔标签
        yellow_coating_label = Text("黄苔", font="SimHei", color=BLACK).scale(0.6)
        yellow_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头

        # 黄苔描述
        yellow_coating_desc = Text(
            "黄苔：正常舌苔由白色变为黄色，提示里热证",
            font="SimHei",
            color=BLACK
        ).scale(0.5)
        yellow_coating_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头

        # 创建黄苔
        yellow_coating = thick_coating.copy()
        yellow_coating.set_fill(rgb_to_color([0.95, 0.9, 0.5]), opacity=0.5)  # 黄色

        # 动画展示：厚腻苔变为黄苔
        self.play(
            FadeOut(thick_coating),
            FadeOut(thick_coating_label),
            FadeOut(thick_coating_desc),
            FadeIn(yellow_coating),
            Write(yellow_coating_label),
            Write(yellow_coating_desc)
        )

        self.wait(2)

        # 灰黑苔标签
        black_coating_label = Text("灰黑苔", font="SimHei", color=BLACK).scale(0.6)
        black_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头

        # 灰黑苔描述
        black_coating_desc = Text(
            "灰黑苔：正常舌苔由白色变为灰黑色，提示热极或寒极",
            font="SimHei",
            color=BLACK
        ).scale(0.5)
        black_coating_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头

        # 创建灰黑苔
        black_coating = yellow_coating.copy()
        black_coating.set_fill(rgb_to_color([0.3, 0.3, 0.3]), opacity=0.5)  # 灰黑色

        # 动画展示：黄苔变为灰黑苔
        self.play(
            FadeOut(yellow_coating),
            FadeOut(yellow_coating_label),
            FadeOut(yellow_coating_desc),
            FadeIn(black_coating),
            Write(black_coating_label),
            Write(black_coating_desc)
        )

        self.wait(2)

        # 恢复正常舌象
        self.play(
            FadeOut(normal_tongue_body),
            FadeOut(black_coating),
            FadeOut(black_coating_label),
            FadeOut(black_coating_desc),
            FadeOut(title),
            FadeIn(normal_tongue)
        )

    def create_abnormal_tongue_from_normal(self, color, coating_color, coating_opacity=0.3, width_scale=1.0, height_scale=1.0, coating_scale=1.0):
        """基于正常舌象创建异常舌象"""
        # 创建正常舌象的各个部分
        tongue_body, tongue_coating = self.create_normal_tongue_parts()

        # 修改舌体颜色
        tongue_body.set_fill(color, opacity=1)

        # 修改舌苔颜色和透明度
        tongue_coating.set_fill(coating_color, opacity=coating_opacity)

        # 调整大小
        tongue_body.scale(width_scale, height_scale)
        tongue_coating.scale(width_scale * coating_scale, height_scale * coating_scale)

        # 组合在一起（不包含舌沟，因为某些异常舌象可能不需要显示舌沟）
        abnormal_tongue = VGroup(tongue_body, tongue_coating)
        abnormal_tongue.move_to(ORIGIN)

        return abnormal_tongue

    def create_teeth_mark_tongue(self):
        """创建齿痕舌"""
        # 基础舌体
        tongue = self.create_abnormal_tongue_from_normal(
            color=rgb_to_color([0.9, 0.6, 0.6]),
            coating_color=rgb_to_color([0.95, 0.95, 0.95]),
            coating_opacity=0.3,
            width_scale=1.1,
            height_scale=1.1
        )

        # 添加齿痕（左右两侧的小凹陷）
        left_mark1 = Line(LEFT * 0.6 + UP * 0.2, LEFT * 0.6 + DOWN * 0.2, color=BLACK)
        left_mark2 = Line(LEFT * 0.8 + UP * 0.15, LEFT * 0.8 + DOWN * 0.15, color=BLACK)
        right_mark1 = Line(RIGHT * 0.6 + UP * 0.2, RIGHT * 0.6 + DOWN * 0.2, color=BLACK)
        right_mark2 = Line(RIGHT * 0.8 + UP * 0.15, RIGHT * 0.8 + DOWN * 0.15, color=BLACK)

        teeth_marks = VGroup(left_mark1, left_mark2, right_mark1, right_mark2)

        return VGroup(tongue, teeth_marks)

    def create_cracked_tongue(self):
        """创建裂纹舌"""
        # 基础舌体
        tongue = self.create_abnormal_tongue_from_normal(
            color=rgb_to_color([0.9, 0.5, 0.5]),  # 略深的红色
            coating_color=rgb_to_color([0.95, 0.95, 0.95]),
            coating_opacity=0.2
        )

        # 添加裂纹
        center_crack = Line(DOWN * 0.3, UP * 0.3, color=BLACK)
        left_crack = Line(LEFT * 0.4 + DOWN * 0.1, LEFT * 0.4 + UP * 0.2, color=BLACK).rotate(PI/6)
        right_crack = Line(RIGHT * 0.4 + DOWN * 0.1, RIGHT * 0.4 + UP * 0.2, color=BLACK).rotate(-PI/6)

        cracks = VGroup(center_crack, left_crack, right_crack)

        return VGroup(tongue, cracks)


class CompleteTongueScene(Scene):
    """完整的舌诊教学场景，包含部位介绍、观察维度、正常舌象和异常舌象"""

    def construct(self):
        # 设置背景为白色
        self.camera.background_color = "#FFFFFF"

        # 第一部分：舌部位解析与观察维度介绍
        self.show_tongue_analysis()

        # 第二部分：正常舌象展示
        self.show_normal_tongue()

        # 第三部分：异常舌象展示
        self.show_abnormal_tongues()

        # 结束
        self.show_conclusion()

    def show_tongue_analysis(self):
        """第一部分：舌部位解析与观察维度介绍"""
        # 创建主标题
        main_title = Text("中医舌诊完整教程", font="SimHei", color=BLACK).scale(1.2)
        main_title.to_edge(UP, buff=0.3)
        self.play(Write(main_title))
        self.wait(1)

        # 创建第一部分标题
        part1_title = Text("第一部分：舌部位解析与观察维度", font="SimHei", color=BLUE).scale(0.8)
        part1_title.next_to(main_title, DOWN, buff=0.5)
        self.play(Write(part1_title))
        self.wait(1)

        # 创建舌头的各个部分 - 调整位置使其更居中
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        tongue_group = VGroup(tongue_body, tongue_coating)
        tongue_group.scale(0.8).move_to(LEFT * 2.5)

        # 创建右侧文本区域背景
        text_bg = Rectangle(
            width=5.5, height=5,
            fill_color=WHITE, fill_opacity=0.9,
            stroke_color=GRAY, stroke_width=1
        ).move_to(RIGHT * 3.5)
        self.play(FadeIn(text_bg))

        # 1. 首先显示舌质
        self.play(FadeIn(tongue_body))

        # 舌质标签和说明
        tongue_body_label = Text("舌质", font="SimHei", color=BLACK).scale(0.7)
        tongue_body_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(tongue_body_label))

        # 舌质说明文本
        tongue_body_text = Text(
            "舌质：舌头的肉质部分\n是舌诊的重要观察对象",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        tongue_body_text.move_to(text_bg.get_center())

        # 添加从文本到舌质的箭头
        body_arrow = Arrow(
            start=tongue_body_text.get_left() + LEFT * 0.2,
            end=tongue_body.get_center() + RIGHT * 0.5,
            color=RED,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_body_text), Create(body_arrow))
        self.wait(2)

        # 2. 然后显示舌苔
        self.play(FadeIn(tongue_coating))

        # 舌苔标签和说明
        tongue_coating_label = Text("舌苔", font="SimHei", color=BLACK).scale(0.7)
        tongue_coating_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(FadeOut(tongue_body_label), Write(tongue_coating_label))

        # 舌苔说明文本
        tongue_coating_text = Text(
            "舌苔：舌头表面的薄薄苔状物\n反映胃肠等消化系统状况",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        tongue_coating_text.move_to(text_bg.get_center())

        # 添加从文本到舌苔的箭头
        coating_arrow = Arrow(
            start=tongue_coating_text.get_left() + LEFT * 0.2,
            end=tongue_coating.get_center() + RIGHT * 0.3,
            color=BLUE,
            buff=0.1,
            stroke_width=3
        )

        self.play(FadeOut(tongue_body_text), FadeOut(body_arrow),
                 Write(tongue_coating_text), Create(coating_arrow))
        self.wait(2)

        # 3. 舌质观察维度
        self.play(FadeOut(tongue_coating), FadeOut(tongue_coating_label),
                 FadeOut(tongue_coating_text), FadeOut(coating_arrow))

        # 重新显示舌质标签
        tongue_body_label = Text("舌质观察维度", font="SimHei", color=BLACK).scale(0.7)
        tongue_body_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(tongue_body_label))

        # 舌质观察维度文本
        dimensions_text = Text(
            "舌质观察维度：\n\n• 颜色：淡红、淡白、红、紫等\n\n• 润泽度：润泽、干燥等\n\n• 形态：正常、胖大、瘦小等",
            font="SimHei",
            color=BLACK,
            line_spacing=1.0
        ).scale(0.5)
        dimensions_text.move_to(text_bg.get_center())
        self.play(Write(dimensions_text))

        # 演示各种维度变化（简化版本）
        self.demonstrate_tongue_dimensions(tongue_body, text_bg)

        self.play(FadeOut(dimensions_text))

        # 4. 舌苔观察维度
        self.play(FadeIn(tongue_coating), FadeOut(tongue_body_label))

        tongue_coating_label = Text("舌苔观察维度", font="SimHei", color=BLACK).scale(0.7)
        tongue_coating_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(tongue_coating_label))

        # 舌苔观察维度文本
        coating_dimensions_text = Text(
            "舌苔观察维度：\n\n• 颜色：白、黄、灰黑等\n\n• 厚度：薄、厚等\n\n• 质地：腻、燥、剥落等",
            font="SimHei",
            color=BLACK,
            line_spacing=1.0
        ).scale(0.5)
        coating_dimensions_text.move_to(text_bg.get_center())
        self.play(Write(coating_dimensions_text))

        # 演示舌苔维度变化（简化版本）
        self.demonstrate_coating_dimensions(tongue_coating, text_bg)

        # 清理第一部分
        self.play(
            FadeOut(tongue_body),
            FadeOut(tongue_coating),
            FadeOut(tongue_coating_label),
            FadeOut(coating_dimensions_text),
            FadeOut(text_bg),
            FadeOut(part1_title)
        )

        # 保存主标题供后续使用
        self.main_title = main_title

    def demonstrate_tongue_dimensions(self, tongue_body, text_bg):
        """演示舌质观察维度变化（简化版本）"""
        # 颜色变化演示
        color_demo = Text("→ 颜色变化", font="SimHei", color=RED).scale(0.6)
        color_demo.move_to(text_bg.get_center() + DOWN * 2)
        self.play(FadeIn(color_demo))

        # 快速演示颜色变化
        colors = [
            rgb_to_color([0.95, 0.85, 0.85]),  # 淡白
            rgb_to_color([0.9, 0.3, 0.3]),     # 红色
            rgb_to_color([0.9, 0.5, 0.5])      # 恢复正常
        ]
        for color in colors:
            self.play(tongue_body.animate.set_color(color), run_time=0.8)
            self.wait(0.5)

        self.play(FadeOut(color_demo))

    def demonstrate_coating_dimensions(self, tongue_coating, text_bg):
        """演示舌苔观察维度变化（简化版本，重点改善厚度表现）"""
        # 厚度变化演示 - 改善表现效果
        thickness_demo = Text("→ 厚度变化", font="SimHei", color=BLUE).scale(0.6)
        thickness_demo.move_to(text_bg.get_center() + DOWN * 2)
        self.play(FadeIn(thickness_demo))

        # 创建厚度指示器
        thickness_indicator = Rectangle(
            width=0.3, height=0.05,
            fill_color=WHITE, fill_opacity=1,
            stroke_color=BLACK, stroke_width=2
        )
        thickness_indicator.next_to(tongue_coating, RIGHT, buff=0.5)

        # 薄苔标签
        thin_label = Text("薄苔", font="SimHei", color=BLACK).scale(0.4)
        thin_label.next_to(thickness_indicator, RIGHT, buff=0.2)

        self.play(FadeIn(thickness_indicator), FadeIn(thin_label))
        self.wait(1)

        # 变为厚苔 - 使用多层效果和厚度指示器
        thick_coating = tongue_coating.copy()
        thick_coating.set_fill(rgb_to_color([0.9, 0.9, 0.9]), opacity=0.8)
        thick_coating.scale(1.1)

        # 厚苔指示器
        thick_indicator = Rectangle(
            width=0.3, height=0.15,  # 增加高度表示厚度
            fill_color=WHITE, fill_opacity=1,
            stroke_color=BLACK, stroke_width=2
        )
        thick_indicator.next_to(tongue_coating, RIGHT, buff=0.5)

        thick_label = Text("厚苔", font="SimHei", color=BLACK).scale(0.4)
        thick_label.next_to(thick_indicator, RIGHT, buff=0.2)

        self.play(
            FadeOut(tongue_coating),
            FadeIn(thick_coating),
            Transform(thickness_indicator, thick_indicator),
            Transform(thin_label, thick_label)
        )
        self.wait(1)

        # 恢复薄苔
        self.play(
            FadeOut(thick_coating),
            FadeIn(tongue_coating),
            FadeOut(thickness_indicator),
            FadeOut(thin_label),
            FadeOut(thickness_demo)
        )

    def show_normal_tongue(self):
        """第二部分：正常舌象展示"""
        # 创建第二部分标题
        part2_title = Text("第二部分：正常舌象", font="SimHei", color=GREEN).scale(0.8)
        part2_title.next_to(self.main_title, DOWN, buff=0.5)
        self.play(Write(part2_title))
        self.wait(1)

        # 创建正常舌象的各个部分
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        tongue_group = VGroup(tongue_body, tongue_coating)
        tongue_group.scale(0.9).move_to(LEFT * 2.5)

        # 创建右侧信息区域背景
        info_bg = Rectangle(
            width=5.5, height=5,
            fill_color=WHITE, fill_opacity=0.9,
            stroke_color=GRAY, stroke_width=1
        ).move_to(RIGHT * 3.5)
        self.play(FadeIn(info_bg))

        # 创建正常舌象标签
        normal_tongue_label = Text("正常舌象", font="SimHei", color=BLACK).scale(0.7)
        normal_tongue_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(normal_tongue_label))

        # 1. 先显示舌质颜色
        self.play(FadeIn(tongue_body))

        # 显示舌质说明文本
        tongue_color_text = Text(
            "舌质淡红明润",
            font="SimHei",
            color=BLACK
        ).scale(0.6)
        tongue_color_text.move_to(info_bg.get_center() + UP * 1.5)

        # 添加从文本到舌质的箭头
        color_arrow = Arrow(
            start=tongue_color_text.get_left() + LEFT * 0.2,
            end=tongue_body.get_center() + RIGHT * 0.5,
            color=RED,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_color_text), Create(color_arrow))
        self.wait(1.5)
        self.play(FadeOut(tongue_color_text), FadeOut(color_arrow))

        # 2. 显示舌苔
        self.play(FadeIn(tongue_coating))

        # 显示舌苔说明文本
        tongue_coating_text = Text(
            "舌苔薄白均匀",
            font="SimHei",
            color=BLACK
        ).scale(0.6)
        tongue_coating_text.move_to(info_bg.get_center() + UP * 1.5)

        # 添加从文本到舌苔的箭头
        coating_arrow = Arrow(
            start=tongue_coating_text.get_left() + LEFT * 0.2,
            end=tongue_coating.get_center() + RIGHT * 0.3,
            color=BLUE,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_coating_text), Create(coating_arrow))
        self.wait(1.5)
        self.play(FadeOut(tongue_coating_text), FadeOut(coating_arrow))

        # 显示完整说明文本
        complete_description = Text(
            "正常舌象特征：\n\n• 舌质淡红明润\n• 舌苔薄白均匀\n• 舌体大小适中\n• 边缘光滑无齿痕",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        complete_description.move_to(info_bg.get_center())

        self.play(Write(complete_description))
        self.wait(2)

        # 保存正常舌象组合供后续使用
        self.normal_tongue = VGroup(tongue_body, tongue_coating)
        self.normal_tongue_label = normal_tongue_label
        self.complete_description = complete_description
        self.info_bg = info_bg
        self.part2_title = part2_title

    def show_abnormal_tongues(self):
        """第三部分：异常舌象展示"""
        # 创建第三部分标题
        part3_title = Text("第三部分：异常舌象", font="SimHei", color=RED).scale(0.8)
        part3_title.next_to(self.main_title, DOWN, buff=0.5)

        # 隐藏正常舌象的描述文本
        self.play(
            FadeOut(self.complete_description),
            FadeOut(self.normal_tongue_label),
            FadeOut(self.part2_title),
            Write(part3_title)
        )

        # 展示舌色异常
        self.show_color_abnormalities_complete(self.normal_tongue, self.info_bg)

        # 展示舌形异常
        self.show_shape_abnormalities_complete(self.normal_tongue, self.info_bg)

        # 展示舌苔异常（重点改善厚度和湿度表现）
        self.show_coating_abnormalities_complete(self.normal_tongue, self.info_bg)

        # 保存第三部分标题
        self.part3_title = part3_title

    def show_color_abnormalities_complete(self, normal_tongue, info_bg):
        """展示舌色异常（完整版）"""
        # 子标题
        subtitle = Text("舌色异常", font="SimHei", color=BLACK).scale(0.7)
        subtitle.next_to(self.main_title, DOWN, buff=1.2)
        self.play(Write(subtitle))

        # 分解正常舌象，只取出舌体
        normal_tongue_body = normal_tongue[0].copy()
        normal_tongue_body.move_to(LEFT * 2.5)

        # 显示正常舌体
        self.play(FadeIn(normal_tongue_body))

        # 演示淡白舌
        pale_tongue_label = Text("淡白舌", font="SimHei", color=BLACK).scale(0.6)
        pale_tongue_label.next_to(normal_tongue_body, DOWN, buff=0.8)

        pale_tongue_desc = Text(
            "淡白舌：正常舌质淡红变为淡白\n提示气血两虚，阳虚",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        pale_tongue_desc.move_to(info_bg.get_center())

        self.play(
            normal_tongue_body.animate.set_fill(rgb_to_color([0.95, 0.85, 0.85])),
            Write(pale_tongue_label),
            Write(pale_tongue_desc)
        )
        self.wait(2)

        # 演示红舌
        red_tongue_label = Text("红舌", font="SimHei", color=BLACK).scale(0.6)
        red_tongue_label.next_to(normal_tongue_body, DOWN, buff=0.8)

        red_tongue_desc = Text(
            "红舌：正常舌质淡红变为鲜红\n提示热证",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        red_tongue_desc.move_to(info_bg.get_center())

        self.play(
            FadeOut(pale_tongue_label),
            FadeOut(pale_tongue_desc),
            normal_tongue_body.animate.set_fill(rgb_to_color([0.9, 0.3, 0.3])),
            Write(red_tongue_label),
            Write(red_tongue_desc)
        )
        self.wait(2)

        # 恢复正常并清理
        self.play(
            FadeOut(normal_tongue_body),
            FadeOut(red_tongue_label),
            FadeOut(red_tongue_desc),
            FadeOut(subtitle),
            FadeIn(normal_tongue)
        )

    def show_shape_abnormalities_complete(self, normal_tongue, info_bg):
        """展示舌形异常（完整版）"""
        # 子标题
        subtitle = Text("舌形异常", font="SimHei", color=BLACK).scale(0.7)
        subtitle.next_to(self.main_title, DOWN, buff=1.2)
        self.play(Write(subtitle))

        # 创建正常舌象的副本
        normal_tongue_copy = normal_tongue.copy()
        normal_tongue_copy.move_to(LEFT * 2.5)

        # 显示正常舌象
        self.play(FadeIn(normal_tongue_copy))

        # 胖大舌演示
        fat_tongue_label = Text("胖大舌", font="SimHei", color=BLACK).scale(0.6)
        fat_tongue_label.next_to(normal_tongue_copy, DOWN, buff=0.8)

        fat_tongue_desc = Text(
            "胖大舌：正常舌体变得胖大\n提示脾肾阳虚",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        fat_tongue_desc.move_to(info_bg.get_center())

        self.play(
            normal_tongue_copy.animate.scale(1.3),
            Write(fat_tongue_label),
            Write(fat_tongue_desc)
        )
        self.wait(2)

        # 恢复正常并清理
        self.play(
            FadeOut(normal_tongue_copy),
            FadeOut(fat_tongue_label),
            FadeOut(fat_tongue_desc),
            FadeOut(subtitle),
            FadeIn(normal_tongue)
        )

    def show_coating_abnormalities_complete(self, normal_tongue, info_bg):
        """展示舌苔异常（完整版，重点改善厚度和湿度表现）"""
        # 子标题
        subtitle = Text("舌苔异常", font="SimHei", color=BLACK).scale(0.7)
        subtitle.next_to(self.main_title, DOWN, buff=1.2)
        self.play(Write(subtitle))

        # 分解正常舌象，取出舌体和舌苔
        normal_tongue_copy = normal_tongue.copy()
        normal_tongue_body = normal_tongue_copy[0]
        normal_tongue_coating = normal_tongue_copy[1]
        normal_tongue_copy.move_to(LEFT * 2.5)

        # 显示正常舌象
        self.play(FadeIn(normal_tongue_copy))

        # 1. 厚腻苔演示 - 改善厚度表现
        thick_coating_label = Text("厚腻苔", font="SimHei", color=BLACK).scale(0.6)
        thick_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.8)

        thick_coating_desc = Text(
            "厚腻苔：正常舌苔变得厚腻\n提示痰湿、食积",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        thick_coating_desc.move_to(info_bg.get_center())

        # 创建多层厚苔效果
        thick_coating_layer1 = normal_tongue_coating.copy()
        thick_coating_layer1.set_fill(rgb_to_color([0.9, 0.9, 0.9]), opacity=0.6)
        thick_coating_layer1.scale(1.1)

        thick_coating_layer2 = normal_tongue_coating.copy()
        thick_coating_layer2.set_fill(rgb_to_color([0.85, 0.85, 0.85]), opacity=0.4)
        thick_coating_layer2.scale(1.05)

        # 创建厚度指示器 - 3D效果
        thickness_base = Rectangle(
            width=0.4, height=0.08,
            fill_color=WHITE, fill_opacity=1,
            stroke_color=BLACK, stroke_width=2
        )
        thickness_top = Rectangle(
            width=0.35, height=0.08,
            fill_color=rgb_to_color([0.95, 0.95, 0.95]), fill_opacity=1,
            stroke_color=BLACK, stroke_width=1
        )
        thickness_top.next_to(thickness_base, UP, buff=0)
        thickness_indicator = VGroup(thickness_base, thickness_top)
        thickness_indicator.next_to(normal_tongue_copy, RIGHT, buff=0.5)

        thick_label = Text("厚苔", font="SimHei", color=BLACK).scale(0.4)
        thick_label.next_to(thickness_indicator, RIGHT, buff=0.2)

        self.play(
            FadeOut(normal_tongue_coating),
            FadeIn(thick_coating_layer2),
            FadeIn(thick_coating_layer1),
            FadeIn(thickness_indicator),
            FadeIn(thick_label),
            Write(thick_coating_label),
            Write(thick_coating_desc)
        )
        self.wait(2)

        # 2. 干燥苔演示 - 改善湿度表现
        dry_coating_label = Text("干燥苔", font="SimHei", color=BLACK).scale(0.6)
        dry_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.8)

        dry_coating_desc = Text(
            "干燥苔：舌苔失去正常润泽\n提示津液不足，热盛伤津",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        dry_coating_desc.move_to(info_bg.get_center())

        # 创建干燥效果 - 裂纹和粗糙纹理
        dry_coating = normal_tongue_coating.copy()
        dry_coating.set_fill(rgb_to_color([0.9, 0.9, 0.8]), opacity=0.8)  # 略黄的干燥色

        # 创建干燥裂纹
        dry_cracks = VGroup()
        crack_positions = [
            (np.array([-0.3, 0.2, 0]), np.array([-0.1, -0.1, 0])),
            (np.array([0.1, 0.3, 0]), np.array([0.3, 0, 0])),
            (np.array([-0.2, -0.2, 0]), np.array([0, -0.4, 0])),
            (np.array([0.2, -0.1, 0]), np.array([0.4, -0.3, 0]))
        ]

        for start, end in crack_positions:
            crack = Line(start, end, color=rgb_to_color([0.7, 0.7, 0.6]), stroke_width=2)
            dry_cracks.add(crack)

        dry_cracks.move_to(normal_tongue_coating.get_center())

        # 创建湿度指示器
        moisture_indicator = VGroup()
        # 干燥状态 - 空的水滴
        dry_drop = Circle(radius=0.08, color=BLACK, stroke_width=2, fill_opacity=0)
        dry_drop.next_to(normal_tongue_copy, RIGHT, buff=0.5)

        # 添加"X"表示无湿度
        cross_line1 = Line(LEFT * 0.05 + UP * 0.05, RIGHT * 0.05 + DOWN * 0.05, color=RED, stroke_width=2)
        cross_line2 = Line(LEFT * 0.05 + DOWN * 0.05, RIGHT * 0.05 + UP * 0.05, color=RED, stroke_width=2)
        cross_line1.move_to(dry_drop.get_center())
        cross_line2.move_to(dry_drop.get_center())

        moisture_indicator.add(dry_drop, cross_line1, cross_line2)

        dry_moisture_label = Text("干燥", font="SimHei", color=BLACK).scale(0.4)
        dry_moisture_label.next_to(moisture_indicator, RIGHT, buff=0.2)

        self.play(
            FadeOut(thick_coating_layer1),
            FadeOut(thick_coating_layer2),
            FadeOut(thickness_indicator),
            FadeOut(thick_label),
            FadeOut(thick_coating_label),
            FadeOut(thick_coating_desc),
            FadeIn(dry_coating),
            FadeIn(dry_cracks),
            FadeIn(moisture_indicator),
            FadeIn(dry_moisture_label),
            Write(dry_coating_label),
            Write(dry_coating_desc)
        )
        self.wait(2)

        # 恢复正常并清理
        self.play(
            FadeOut(normal_tongue_body),
            FadeOut(dry_coating),
            FadeOut(dry_cracks),
            FadeOut(moisture_indicator),
            FadeOut(dry_moisture_label),
            FadeOut(dry_coating_label),
            FadeOut(dry_coating_desc),
            FadeOut(subtitle),
            FadeIn(normal_tongue)
        )

    def show_conclusion(self):
        """结论部分"""
        # 创建结论标题
        conclusion_title = Text("总结", font="SimHei", color=PURPLE).scale(0.8)
        conclusion_title.next_to(self.main_title, DOWN, buff=0.5)

        self.play(
            FadeOut(self.part3_title),
            Write(conclusion_title)
        )

        # 总结文本
        conclusion_text = Text(
            "舌诊是中医诊断的重要方法\n\n通过观察舌质和舌苔的变化\n可以了解人体内部的健康状况\n\n• 舌质反映脏腑气血状态\n• 舌苔反映胃肠功能状况\n• 综合分析有助于准确诊断",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.6)
        conclusion_text.move_to(self.info_bg.get_center())

        self.play(Write(conclusion_text))
        self.wait(3)

        # 最终淡出
        self.play(
            FadeOut(self.normal_tongue),
            FadeOut(conclusion_text),
            FadeOut(self.info_bg),
            FadeOut(conclusion_title),
            FadeOut(self.main_title)
        )

        # 结束感谢
        thanks_text = Text("谢谢观看！", font="SimHei", color=BLACK).scale(1.5)
        self.play(Write(thanks_text))
        self.wait(2)
        self.play(FadeOut(thanks_text))

    def create_normal_tongue_parts(self):
        """创建正常舌象的各个部分，便于逐步显示"""
        # 简化SVG路径，创建一个更简单的舌头轮廓
        tongue_outline = VMobject(color=BLACK, stroke_width=1.5)

        # 定义舌头轮廓的关键点
        # 根据SVG路径数据，舌头是一个长形，底部宽，顶部稍窄

        # 顶部点（舌根）
        top_left = np.array([-0.8, 0.8, 0])
        top_right = np.array([0.8, 0.8, 0])

        # 底部点（舌尖）
        bottom_left = np.array([-0.6, -1.2, 0])  # 舌尖更长
        bottom_right = np.array([0.6, -1.2, 0])

        # 使用贝塞尔曲线创建平滑的舌头轮廓

        # 左侧曲线 - 从舌根到舌尖的左侧
        left_curve = CubicBezier(
            start_anchor=top_left,
            start_handle=np.array([-1.0, 0.5, 0]),
            end_handle=np.array([-1.2, -0.5, 0]),
            end_anchor=bottom_left
        )

        # 底部曲线 - 舌尖部分
        bottom_curve = CubicBezier(
            start_anchor=bottom_left,
            start_handle=np.array([-0.3, -1.6, 0]),
            end_handle=np.array([0.3, -1.6, 0]),
            end_anchor=bottom_right
        )

        # 右侧曲线 - 从舌尖到舌根的右侧
        right_curve = CubicBezier(
            start_anchor=bottom_right,
            start_handle=np.array([1.2, -0.5, 0]),
            end_handle=np.array([1.0, 0.5, 0]),
            end_anchor=top_right
        )

        # 顶部曲线 - 舌根部分
        top_curve = CubicBezier(
            start_anchor=top_right,
            start_handle=np.array([0.5, 1.0, 0]),
            end_handle=np.array([-0.5, 1.0, 0]),
            end_anchor=top_left
        )

        # 组合所有曲线形成完整的舌头轮廓
        tongue_outline.append_points(left_curve.points)
        tongue_outline.append_points(bottom_curve.points)
        tongue_outline.append_points(right_curve.points)
        tongue_outline.append_points(top_curve.points)
        tongue_outline.close_path()

        # 填充舌体 - 粉红色
        tongue_body = tongue_outline.copy()
        tongue_body.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=1)  # 粉红色

        # 正常舌苔 - 薄白色并覆盖大部分舌面
        tongue_coating = tongue_outline.copy()
        tongue_coating.scale(0.85)  # 稍小一点的舌苔区域
        tongue_coating.set_fill(rgb_to_color([0.95, 0.95, 0.95]), opacity=0.2)  # 薄白色

        # 调整大小和位置
        tongue_body.scale(1.0).move_to(ORIGIN)
        tongue_coating.scale(1.0).move_to(ORIGIN)

        return tongue_body, tongue_coating