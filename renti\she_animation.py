from manim import *
import numpy as np

class TongueAnalysisScene(Scene):
    """舌的部位解析和观察维度介绍"""
    
    def construct(self):
        # 设置背景为白色
        self.camera.background_color = "#FFFFFF"
        
        # 创建标题
        title = Text("舌部位解析与观察维度", font="SimHei", color=BLACK).scale(1.2)
        title.to_edge(UP, buff=0.5)
        self.play(Write(title))
        
        # 创建舌头的各个部分
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        
        # 1. 首先显示舌质
        self.play(FadeIn(tongue_body))
        
        # 舌质标签和说明
        tongue_body_label = Text("舌质", font="SimHei", color=BLACK).scale(0.8)
        tongue_body_label.next_to(tongue_body, DOWN, buff=0.5)
        self.play(Write(tongue_body_label))
        
        # 舌质说明文本
        tongue_body_text = Text(
            "舌质：舌头的肉质部分，\n是舌诊的重要观察对象", 
            font="SimHei", 
            color=BLACK
        ).scale(0.6)
        tongue_body_text.to_edge(RIGHT, buff=1.5).shift(UP * 1)
        
        # 添加从文本到舌质的箭头
        body_arrow = Arrow(
            start=tongue_body_text.get_left(),
            end=tongue_body.get_right(),
            color=RED,
            buff=0.1,
            stroke_width=2
        )
        
        self.play(Write(tongue_body_text), Create(body_arrow))
        self.wait(2)
        
        # 2. 然后显示舌苔
        self.play(FadeIn(tongue_coating))
        
        # 舌苔标签和说明
        tongue_coating_label = Text("舌苔", font="SimHei", color=BLACK).scale(0.8)
        tongue_coating_label.next_to(tongue_body, DOWN, buff=0.5)
        self.play(FadeOut(tongue_body_label), Write(tongue_coating_label))
        
        # 舌苔说明文本
        tongue_coating_text = Text(
            "舌苔：舌头表面的一层薄薄苔状物，\n反映胃肠等消化系统状况", 
            font="SimHei", 
            color=BLACK
        ).scale(0.6)
        tongue_coating_text.to_edge(RIGHT, buff=1.5).shift(UP * 1)
        
        # 添加从文本到舌苔的箭头
        coating_arrow = Arrow(
            start=tongue_coating_text.get_left(),
            end=tongue_coating.get_right(),
            color=BLUE,
            buff=0.1,
            stroke_width=2
        )
        
        self.play(FadeOut(tongue_body_text), FadeOut(body_arrow), 
                 Write(tongue_coating_text), Create(coating_arrow))
        self.wait(2)
        
        # 3. 舌质观察维度
        self.play(FadeOut(tongue_coating), FadeOut(tongue_coating_label), 
                 FadeOut(tongue_coating_text), FadeOut(coating_arrow))
        
        # 重新显示舌质
        tongue_body_label = Text("舌质观察维度", font="SimHei", color=BLACK).scale(0.8)
        tongue_body_label.next_to(tongue_body, DOWN, buff=0.5)
        self.play(Write(tongue_body_label))
        
        # 舌质观察维度文本
        dimensions_text = Text(
            "舌质观察维度：\n· 颜色（淡红、淡白、红、紫等）\n· 润泽度（润泽、干燥等）\n· 形态（正常、胖大、瘦小等）", 
            font="SimHei", 
            color=BLACK
        ).scale(0.6)
        dimensions_text.to_edge(RIGHT, buff=1.5).shift(UP * 1)
        self.play(Write(dimensions_text))
        
        # 颜色维度动画展示
        color_highlight = Text("颜色", font="SimHei", color=RED).scale(0.6)
        # 计算颜色文本的位置
        color_pos = dimensions_text.get_center() + UP * 0.3 + LEFT * 1.5
        color_highlight.move_to(color_pos)
        self.play(tongue_body.animate.set_color(rgb_to_color([0.9, 0.5, 0.5])), 
                  FadeIn(color_highlight))
        self.wait(1)
        self.play(tongue_body.animate.set_color(rgb_to_color([0.95, 0.85, 0.85])))
        self.wait(1)
        self.play(tongue_body.animate.set_color(rgb_to_color([0.9, 0.3, 0.3])))
        self.wait(1)
        self.play(tongue_body.animate.set_color(rgb_to_color([0.9, 0.5, 0.5])), 
                  FadeOut(color_highlight))
        
        # 润泽度维度动画展示
        moisture_highlight = Text("润泽度", font="SimHei", color=BLUE).scale(0.6)
        # 计算润泽度文本的位置
        moisture_pos = dimensions_text.get_center() + LEFT * 1.3
        moisture_highlight.move_to(moisture_pos)
        
        # 创建干燥效果
        dry_tongue = tongue_body.copy()
        dry_tongue.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=0.8)
        dry_lines = VGroup()
        for i in range(5):
            line = Line(
                start=np.array([-0.5 + 0.2 * i, -0.5, 0]),
                end=np.array([-0.3 + 0.2 * i, 0.5, 0]),
                color=rgb_to_color([0.8, 0.4, 0.4]),
                stroke_width=1
            )
            dry_lines.add(line)
        
        self.play(FadeIn(moisture_highlight))
        self.wait(1)
        self.play(FadeOut(tongue_body), FadeIn(dry_tongue), FadeIn(dry_lines))
        self.wait(1)
        self.play(FadeOut(dry_tongue), FadeOut(dry_lines), FadeIn(tongue_body), 
                  FadeOut(moisture_highlight))
        
        # 形态维度动画展示
        shape_highlight = Text("形态", font="SimHei", color=GREEN).scale(0.6)
        # 计算形态文本的位置
        shape_pos = dimensions_text.get_center() + DOWN * 0.3 + LEFT * 1.5
        shape_highlight.move_to(shape_pos)
        
        self.play(FadeIn(shape_highlight))
        self.wait(1)
        self.play(tongue_body.animate.scale(1.3))  # 胖大舌
        self.wait(1)
        self.play(tongue_body.animate.scale(0.7))  # 瘦小舌
        self.wait(1)
        self.play(tongue_body.animate.scale(1/0.7))  # 恢复正常
        self.play(FadeOut(shape_highlight))
        
        self.wait(1)
        self.play(FadeOut(dimensions_text))
        
        # 4. 舌苔观察维度
        self.play(FadeIn(tongue_coating), FadeOut(tongue_body_label))
        
        tongue_coating_label = Text("舌苔观察维度", font="SimHei", color=BLACK).scale(0.8)
        tongue_coating_label.next_to(tongue_body, DOWN, buff=0.5)
        self.play(Write(tongue_coating_label))
        
        # 舌苔观察维度文本
        coating_dimensions_text = Text(
            "舌苔观察维度：\n· 颜色（白、黄、灰黑等）\n· 厚度（薄、厚等）\n· 质地（腻、燥、剥落等）", 
            font="SimHei", 
            color=BLACK
        ).scale(0.6)
        coating_dimensions_text.to_edge(RIGHT, buff=1.5).shift(UP * 1)
        self.play(Write(coating_dimensions_text))
        
        # 颜色维度动画展示
        color_highlight = Text("颜色", font="SimHei", color=RED).scale(0.6)
        # 计算颜色文本的位置
        color_pos = coating_dimensions_text.get_center() + UP * 0.3 + LEFT * 1.5
        color_highlight.move_to(color_pos)
        self.play(FadeIn(color_highlight))
        self.wait(1)
        self.play(tongue_coating.animate.set_color(rgb_to_color([0.95, 0.9, 0.5])))  # 黄苔
        self.wait(1)
        self.play(tongue_coating.animate.set_color(rgb_to_color([0.3, 0.3, 0.3])))  # 灰黑苔
        self.wait(1)
        self.play(tongue_coating.animate.set_color(rgb_to_color([0.95, 0.95, 0.95])), 
                  FadeOut(color_highlight))  # 恢复白苔
        
        # 厚度维度动画展示
        thickness_highlight = Text("厚度", font="SimHei", color=BLUE).scale(0.6)
        # 计算厚度文本的位置
        thickness_pos = coating_dimensions_text.get_center() + LEFT * 1.5
        thickness_highlight.move_to(thickness_pos)
        self.play(FadeIn(thickness_highlight))
        self.wait(1)
        self.play(tongue_coating.animate.set_opacity(0.7))  # 厚苔
        self.wait(1)
        self.play(tongue_coating.animate.set_opacity(0.2))  # 恢复薄苔
        self.play(FadeOut(thickness_highlight))
        
        # 质地维度动画展示
        texture_highlight = Text("质地", font="SimHei", color=GREEN).scale(0.6)
        # 计算质地文本的位置
        texture_pos = coating_dimensions_text.get_center() + DOWN * 0.3 + LEFT * 1.5
        texture_highlight.move_to(texture_pos)
        
        # 创建腻苔效果
        greasy_coating = tongue_coating.copy()
        greasy_coating.set_opacity(0.5)
        
        # 创建剥落苔效果
        peeling_coating = tongue_coating.copy()
        peeling_coating.set_opacity(0.3)
        
        # 创建剥落区域
        peeling_area = Polygon(
            np.array([-0.3, 0, 0]),
            np.array([0.3, 0, 0]),
            np.array([0.2, -0.5, 0]),
            np.array([-0.2, -0.5, 0]),
            color=rgb_to_color([0.9, 0.5, 0.5]),
            fill_opacity=1
        )
        
        self.play(FadeIn(texture_highlight))
        self.wait(1)
        self.play(FadeOut(tongue_coating), FadeIn(greasy_coating))  # 腻苔
        self.wait(1)
        self.play(FadeOut(greasy_coating), FadeIn(peeling_coating), FadeIn(peeling_area))  # 剥落苔
        self.wait(1)
        self.play(FadeOut(peeling_coating), FadeOut(peeling_area), FadeIn(tongue_coating), 
                  FadeOut(texture_highlight))
        
        self.wait(1)
        self.play(FadeOut(coating_dimensions_text))
        
        # 总结
        conclusion_text = Text(
            "舌诊是中医诊断的重要方法，\n通过观察舌质和舌苔的变化，\n可以了解人体内部的健康状况。", 
            font="SimHei", 
            color=BLACK
        ).scale(0.7)
        conclusion_text.to_edge(DOWN, buff=1.0)
        self.play(Write(conclusion_text))
        
        self.wait(2)
        
        # 淡出所有内容
        self.play(
            FadeOut(tongue_body),
            FadeOut(tongue_coating),
            FadeOut(tongue_coating_label),
            FadeOut(conclusion_text),
            FadeOut(title)
        )
    
    def create_normal_tongue_parts(self):
        """创建正常舌象的各个部分，便于逐步显示"""
        # 简化SVG路径，创建一个更简单的舌头轮廓
        tongue_outline = VMobject(color=BLACK, stroke_width=1.5)
        
        # 定义舌头轮廓的关键点
        # 根据SVG路径数据，舌头是一个长形，底部宽，顶部稍窄
        
        # 顶部点（舌根）
        top_center = np.array([0, 0.8, 0])
        top_left = np.array([-0.8, 0.8, 0])
        top_right = np.array([0.8, 0.8, 0])
        
        # 底部点（舌尖）
        bottom_left = np.array([-0.6, -1.2, 0])  # 舌尖更长
        bottom_center = np.array([0, -1.5, 0])  # 舌尖位置更低
        bottom_right = np.array([0.6, -1.2, 0])
        
        # 使用贝塞尔曲线创建平滑的舌头轮廓
        
        # 左侧曲线 - 从舌根到舌尖的左侧
        left_curve = CubicBezier(
            start_anchor=top_left,
            start_handle=np.array([-1.0, 0.5, 0]),
            end_handle=np.array([-1.2, -0.5, 0]),
            end_anchor=bottom_left
        )
        
        # 底部曲线 - 舌尖部分
        bottom_curve = CubicBezier(
            start_anchor=bottom_left,
            start_handle=np.array([-0.3, -1.6, 0]),
            end_handle=np.array([0.3, -1.6, 0]),
            end_anchor=bottom_right
        )
        
        # 右侧曲线 - 从舌尖到舌根的右侧
        right_curve = CubicBezier(
            start_anchor=bottom_right,
            start_handle=np.array([1.2, -0.5, 0]),
            end_handle=np.array([1.0, 0.5, 0]),
            end_anchor=top_right
        )
        
        # 顶部曲线 - 舌根部分
        top_curve = CubicBezier(
            start_anchor=top_right,
            start_handle=np.array([0.5, 1.0, 0]),
            end_handle=np.array([-0.5, 1.0, 0]),
            end_anchor=top_left
        )
        
        # 组合所有曲线形成完整的舌头轮廓
        tongue_outline.append_points(left_curve.points)
        tongue_outline.append_points(bottom_curve.points)
        tongue_outline.append_points(right_curve.points)
        tongue_outline.append_points(top_curve.points)
        tongue_outline.close_path()
        
        # 填充舌体 - 粉红色
        tongue_body = tongue_outline.copy()
        tongue_body.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=1)  # 粉红色
        
        # 正常舌苔 - 薄白色并覆盖大部分舌面
        tongue_coating = tongue_outline.copy()
        tongue_coating.scale(0.85)  # 稍小一点的舌苔区域
        tongue_coating.set_fill(rgb_to_color([0.95, 0.95, 0.95]), opacity=0.2)  # 薄白色
        
        # 调整大小和位置
        tongue_body.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小
        tongue_coating.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小
        
        return tongue_body, tongue_coating

class TongueAnimation(Scene):
    """中医舌诊动画类"""
    
    def construct(self):
        # 设置背景为白色
        self.camera.background_color = "#FFFFFF"
        
        # 创建标题
        title = Text("中医察舌", font="SimHei", color=BLACK).scale(1.2)
        title.to_edge(UP, buff=0.5)
        self.play(Write(title))
        
        # 创建正常舌象的各个部分
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        
        # 创建正常舌象标签
        normal_tongue_label = Text("正常舌象", font="SimHei", color=BLACK).scale(0.7)
        normal_tongue_label.next_to(tongue_body, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        self.play(Write(normal_tongue_label))
        

        
        # 1. 先显示舌质颜色
        self.play(FadeIn(tongue_body))
        
        # 显示舌质说明文本
        tongue_color_text = Text(
            "舌质淡红明润", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        tongue_color_text.to_edge(RIGHT, buff=1.5).shift(UP * 1)
        
        # 添加从文本到舌质的箭头
        color_arrow = Arrow(
            start=tongue_color_text.get_left(), # Start from the left of the text
            end=tongue_body.get_right(),      # Point to the right edge of the tongue body
            color=RED,
            buff=0.1,                         # Smaller buffer to get closer
            stroke_width=2
        )
        
        self.play(Write(tongue_color_text), Create(color_arrow))
        self.wait(1.5) # Slightly longer wait to read
        self.play(FadeOut(tongue_color_text), FadeOut(color_arrow))
        self.wait(0.2) # Short pause before next item
        
        # 2. 显示舌苔
        self.play(FadeIn(tongue_coating))
        
        # 显示舌苔说明文本
        tongue_coating_text = Text(
            "舌苔薄白均匀", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        tongue_coating_text.next_to(tongue_color_text, DOWN, buff=0.3)
        
        # 添加从文本到舌苔的箭头
        coating_arrow = Arrow(
            start=tongue_coating_text.get_left(), # Start from the left of the text
            end=tongue_coating.get_right(),       # Point to the right edge of the tongue coating
            color=BLUE,
            buff=0.1,                           # Smaller buffer
            stroke_width=2
        )
        
        self.play(Write(tongue_coating_text), Create(coating_arrow))
        self.wait(1.5)
        self.play(FadeOut(tongue_coating_text), FadeOut(coating_arrow))
        self.wait(0.2)
        
        # 移除舌沟的显示
        self.wait(0.2)
        
        # 显示完整说明文本
        complete_description = Text(
            "正常舌象特征：\n· 舌质淡红明润\n· 舌苔薄白均匀\n· 舌体大小适中\n· 边缘光滑无齿痕", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        complete_description.to_edge(RIGHT, buff=1.5).shift(UP * 1)
        
        self.play(Write(complete_description))
        self.wait(2)
        
        # 将所有部分组合成一个完整的舌象
        normal_tongue = VGroup(tongue_body, tongue_coating)  # 移除舌沟
        
        # 展示异常舌象
        self.show_abnormal_tongues(normal_tongue, complete_description, normal_tongue_label)
        
        # 结束动画
        self.wait(2)
    

    
    def create_normal_tongue(self):
        """创建正常舌象，根据SVG路径数据创建"""
        tongue_body, tongue_coating = self.create_normal_tongue_parts()  # 移除舌沟
        
        # 组合在一起
        normal_tongue = VGroup(tongue_body, tongue_coating)  # 移除舌沟
        
        # 调整大小和位置
        normal_tongue.scale(1.2)  # 调整整体大小，从0.8改为1.2使舌头更大
        normal_tongue.move_to(ORIGIN)
        
        return normal_tongue
        
    def create_normal_tongue_parts(self):
        """创建正常舌象的各个部分，便于逐步显示"""
        # 简化SVG路径，创建一个更简单的舌头轮廓
        tongue_outline = VMobject(color=BLACK, stroke_width=1.5)
        
        # 定义舌头轮廓的关键点
        # 根据SVG路径数据，舌头是一个长形，底部宽，顶部稍窄
        
        # 顶部点（舌根）
        top_center = np.array([0, 0.8, 0])
        top_left = np.array([-0.8, 0.8, 0])
        top_right = np.array([0.8, 0.8, 0])
        
        # 底部点（舌尖）
        bottom_left = np.array([-0.6, -1.2, 0])  # 舌尖更长
        bottom_center = np.array([0, -1.5, 0])  # 舌尖位置更低
        bottom_right = np.array([0.6, -1.2, 0])
        
        # 使用贝塞尔曲线创建平滑的舌头轮廓
        
        # 左侧曲线 - 从舌根到舌尖的左侧
        left_curve = CubicBezier(
            start_anchor=top_left,
            start_handle=np.array([-1.0, 0.5, 0]),
            end_handle=np.array([-1.2, -0.5, 0]),
            end_anchor=bottom_left
        )
        
        # 底部曲线 - 舌尖部分
        bottom_curve = CubicBezier(
            start_anchor=bottom_left,
            start_handle=np.array([-0.3, -1.6, 0]),
            end_handle=np.array([0.3, -1.6, 0]),
            end_anchor=bottom_right
        )
        
        # 右侧曲线 - 从舌尖到舌根的右侧
        right_curve = CubicBezier(
            start_anchor=bottom_right,
            start_handle=np.array([1.2, -0.5, 0]),
            end_handle=np.array([1.0, 0.5, 0]),
            end_anchor=top_right
        )
        
        # 顶部曲线 - 舌根部分
        top_curve = CubicBezier(
            start_anchor=top_right,
            start_handle=np.array([0.5, 1.0, 0]),
            end_handle=np.array([-0.5, 1.0, 0]),
            end_anchor=top_left
        )
        
        # 组合所有曲线形成完整的舌头轮廓
        tongue_outline.append_points(left_curve.points)
        tongue_outline.append_points(bottom_curve.points)
        tongue_outline.append_points(right_curve.points)
        tongue_outline.append_points(top_curve.points)
        tongue_outline.close_path()
        
        # 填充舌体 - 粉红色
        tongue_body = tongue_outline.copy()
        tongue_body.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=1)  # 粉红色
        
        # 移除舌沟的创建
        
        # 正常舌苔 - 薄白色并覆盖大部分舌面
        tongue_coating = tongue_outline.copy()
        tongue_coating.scale(0.85)  # 稍小一点的舌苔区域
        tongue_coating.set_fill(rgb_to_color([0.95, 0.95, 0.95]), opacity=0.2)  # 薄白色
        
        # 调整大小和位置
        tongue_body.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小
        tongue_coating.scale(1.0).move_to(ORIGIN)  # 从0.8改为1.0，不再缩小
        
        return tongue_body, tongue_coating  # 移除舌沟
        
    def show_abnormal_tongues(self, normal_tongue, complete_description, normal_tongue_label):
        """展示异常舌象"""
        # 创建分类标题
        category_title = Text("异常舌象分类", font="SimHei", color=BLACK).scale(0.8)
        category_title.to_edge(UP, buff=2)
        
        # 隐藏正常舌象的描述文本
        self.play(FadeOut(complete_description), FadeOut(normal_tongue_label))
        
        # 展示舌色异常
        self.show_color_abnormalities(normal_tongue)
        
        # 展示舌形异常
        self.show_shape_abnormalities(normal_tongue)
        
        # 展示舌苔异常
        self.show_coating_abnormalities(normal_tongue)
        
        # 恢复正常舌象的描述文本
        self.play(FadeIn(complete_description), FadeIn(normal_tongue_label))
    
    
    def show_color_abnormalities(self, normal_tongue):
        """展示舌色异常"""
        # 标题
        title = Text("舌色异常", font="SimHei", color=BLACK).scale(0.8)
        title.to_edge(UP, buff=2.0)  # 调整标题位置，防止与主标题重叠
        
        # 先显示标题
        self.play(Write(title))
        
        # 分解正常舌象，只取出舌体，不显示舌苔和舌沟
        normal_tongue_body = normal_tongue[0].copy()  # 只复制舌体
        
        # 只使用舌体，不包含舌苔和舌沟
        normal_tongue_without_coating = normal_tongue_body
        normal_tongue_without_coating.move_to(ORIGIN)
        
        # 显示正常舌体（无舌苔）
        self.play(FadeIn(normal_tongue_without_coating))
        self.wait(1)
        
        # 创建淡白舌体（无舌苔）
        pale_tongue_body = normal_tongue_body.copy()
        pale_tongue_body.set_fill(rgb_to_color([0.95, 0.85, 0.85]), opacity=1)  # 淡白色
        
        # 只使用淡白舌体，不包含舌沟
        pale_tongue_without_coating = pale_tongue_body
        
        # 淡白舌标签
        pale_tongue_label = Text("淡白舌", font="SimHei", color=BLACK).scale(0.6)
        pale_tongue_label.next_to(pale_tongue_without_coating, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 淡白舌描述文字，调整位置避免重叠
        pale_tongue_desc = Text(
            "淡白舌：正常舌质淡红变为淡白，提示气血两虚，阳虚", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        pale_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头  # 将描述文字下移，避免与标题重叠
        
        # 使用颜色渐变而非变形来展示舌质颜色变化
        self.play(
            normal_tongue_body.animate.set_fill(rgb_to_color([0.95, 0.85, 0.85])),  # 直接改变颜色
            Write(pale_tongue_label),
            Write(pale_tongue_desc)
        )
        
        self.wait(2)
        
        # 红舌标签
        red_tongue_label = Text("红舌", font="SimHei", color=BLACK).scale(0.6)
        red_tongue_label.next_to(normal_tongue_without_coating, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 红舌描述文字
        red_tongue_desc = Text(
            "红舌：正常舌质淡红变为鲜红，提示热证", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        red_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头  # 保持与淡白舌描述相同的位置
        
        # 使用颜色渐变展示从淡白舌变为红舌
        self.play(
            FadeOut(pale_tongue_label),
            FadeOut(pale_tongue_desc),
            normal_tongue_body.animate.set_fill(rgb_to_color([0.9, 0.3, 0.3])),  # 红色
            Write(red_tongue_label),
            Write(red_tongue_desc)
        )
        
        self.wait(2)
        
        # 紫舌标签
        purple_tongue_label = Text("紫舌", font="SimHei", color=BLACK).scale(0.6)
        purple_tongue_label.next_to(normal_tongue_without_coating, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 紫舌描述文字
        purple_tongue_desc = Text(
            "紫舌：正常舌质淡红变为紫色，提示血瘡、寒凝", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        purple_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头  # 保持与前面描述相同的位置
        
        # 使用颜色渐变展示从红舌变为紫舌
        self.play(
            FadeOut(red_tongue_label),
            FadeOut(red_tongue_desc),
            normal_tongue_body.animate.set_fill(rgb_to_color([0.6, 0.3, 0.6])),  # 紫色
            Write(purple_tongue_label),
            Write(purple_tongue_desc)
        )
        
        self.wait(2)
        
        # 恢复正常舌象
        self.play(
            FadeOut(normal_tongue_without_coating),
            FadeOut(purple_tongue_label),
            FadeOut(purple_tongue_desc),
            FadeOut(title),
            FadeIn(normal_tongue)
        )
    
    def show_shape_abnormalities(self, normal_tongue):
        """展示舌形异常"""
        # 标题
        title = Text("舌形异常", font="SimHei", color=BLACK).scale(0.8)
        title.to_edge(UP, buff=2.0)  # 调整标题位置，防止与主标题重叠
        
        # 先显示标题
        self.play(Write(title))
        
        # 创建正常舌象的副本
        normal_tongue_copy = normal_tongue.copy()
        normal_tongue_copy.move_to(ORIGIN)
        
        # 显示正常舌象
        self.play(FadeIn(normal_tongue_copy))
        self.wait(1)
        
        # 胖大舌标签
        fat_tongue_label = Text("胖大舌", font="SimHei", color=BLACK).scale(0.6)
        fat_tongue_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 胖大舌描述
        fat_tongue_desc = Text(
            "胖大舌：正常舌体变得胖大，提示脂肾阳虚", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        fat_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头  # 调整位置避免重叠
        
        # 使用缩放动画展示从正常舌象到胖大舌的变化
        self.play(
            normal_tongue_copy.animate.scale(1.3),  # 水平和垂直方向都放大
            Write(fat_tongue_label),
            Write(fat_tongue_desc)
        )
        
        self.wait(2)
        
        # 齿痕舌标签
        teeth_mark_tongue_label = Text("齿痕舌", font="SimHei", color=BLACK).scale(0.6)
        teeth_mark_tongue_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 齿痕舌描述
        teeth_mark_tongue_desc = Text(
            "齿痕舌：正常舌体边缘出现齿痕，提示脂虚湿盛", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        teeth_mark_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头
        
        # 创建齿痕
        left_mark1 = Line(LEFT * 0.6 + UP * 0.2, LEFT * 0.6 + DOWN * 0.2, color=BLACK)
        left_mark2 = Line(LEFT * 0.8 + UP * 0.15, LEFT * 0.8 + DOWN * 0.15, color=BLACK)
        right_mark1 = Line(RIGHT * 0.6 + UP * 0.2, RIGHT * 0.6 + DOWN * 0.2, color=BLACK)
        right_mark2 = Line(RIGHT * 0.8 + UP * 0.15, RIGHT * 0.8 + DOWN * 0.15, color=BLACK)
        teeth_marks = VGroup(left_mark1, left_mark2, right_mark1, right_mark2)
        teeth_marks.scale(1.3)  # 与胖大舌的缩放比例一致
        
        # 动画展示：添加齿痕
        self.play(
            FadeOut(fat_tongue_label),
            FadeOut(fat_tongue_desc),
            FadeIn(teeth_marks),
            Write(teeth_mark_tongue_label),
            Write(teeth_mark_tongue_desc)
        )
        
        self.wait(2)
        
        # 裂纹舌标签
        cracked_tongue_label = Text("裂纹舌", font="SimHei", color=BLACK).scale(0.6)
        cracked_tongue_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 裂纹舌描述
        cracked_tongue_desc = Text(
            "裂纹舌：正常舌面出现裂纹，提示阴血亡虚", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        cracked_tongue_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头
        
        # 创建裂纹
        center_crack = Line(DOWN * 0.3, UP * 0.3, color=BLACK)
        left_crack = Line(LEFT * 0.4 + DOWN * 0.1, LEFT * 0.4 + UP * 0.2, color=BLACK).rotate(PI/6)
        right_crack = Line(RIGHT * 0.4 + DOWN * 0.1, RIGHT * 0.4 + UP * 0.2, color=BLACK).rotate(-PI/6)
        cracks = VGroup(center_crack, left_crack, right_crack)
        cracks.scale(1.3)  # 与胖大舌的缩放比例一致
        
        # 动画展示：替换齿痕为裂纹
        self.play(
            FadeOut(teeth_marks),
            FadeOut(teeth_mark_tongue_label),
            FadeOut(teeth_mark_tongue_desc),
            FadeIn(cracks),
            Write(cracked_tongue_label),
            Write(cracked_tongue_desc)
        )
        
        self.wait(2)
        
        # 恢复正常舌象
        self.play(
            FadeOut(normal_tongue_copy),
            FadeOut(cracks),
            FadeOut(cracked_tongue_label),
            FadeOut(cracked_tongue_desc),
            FadeOut(title),
            FadeIn(normal_tongue)
        )
        
    def show_coating_abnormalities(self, normal_tongue):
        """展示舌苔异常"""
        # 标题
        title = Text("舌苔异常", font="SimHei", color=BLACK).scale(0.8)
        title.to_edge(UP, buff=2.0)  # 调整标题位置，防止与主标题重叠
        
        # 先显示标题
        self.play(Write(title))
        
        # 分解正常舌象，取出舌体和舌苔，不包含舌沟
        normal_tongue_copy = normal_tongue.copy()
        normal_tongue_body = normal_tongue_copy[0]  # 舌体
        normal_tongue_coating = normal_tongue_copy[1]  # 舌苔
        
        # 组合在一起
        normal_tongue_copy.move_to(ORIGIN)
        
        # 显示正常舌象
        self.play(FadeIn(normal_tongue_copy))
        self.wait(1)
        
        # 厚腻苔标签
        thick_coating_label = Text("厚腻苔", font="SimHei", color=BLACK).scale(0.6)
        thick_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 厚腻苔描述
        thick_coating_desc = Text(
            "厚腻苔：正常舌苔变得厚腻，提示痰湿、食积", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        thick_coating_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头  # 调整位置避免重叠
        
        # 创建厚腻苔（将厚度增加，不改变舌体颜色）
        thick_coating = normal_tongue_coating.copy()
        thick_coating.set_fill(rgb_to_color([0.9, 0.9, 0.9]), opacity=0.7)  # 增加不透明度
        thick_coating.scale(1.2)  # 增加舌苔大小
        
        # 动画展示：正常舌苔变为厚腻苔
        self.play(
            FadeOut(normal_tongue_coating),
            FadeIn(thick_coating),
            Write(thick_coating_label),
            Write(thick_coating_desc)
        )
        
        self.wait(2)
        
        # 黄苔标签
        yellow_coating_label = Text("黄苔", font="SimHei", color=BLACK).scale(0.6)
        yellow_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 黄苔描述
        yellow_coating_desc = Text(
            "黄苔：正常舌苔由白色变为黄色，提示里热证", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        yellow_coating_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头
        
        # 创建黄苔
        yellow_coating = thick_coating.copy()
        yellow_coating.set_fill(rgb_to_color([0.95, 0.9, 0.5]), opacity=0.5)  # 黄色
        
        # 动画展示：厚腻苔变为黄苔
        self.play(
            FadeOut(thick_coating),
            FadeOut(thick_coating_label),
            FadeOut(thick_coating_desc),
            FadeIn(yellow_coating),
            Write(yellow_coating_label),
            Write(yellow_coating_desc)
        )
        
        self.wait(2)
        
        # 灰黑苔标签
        black_coating_label = Text("灰黑苔", font="SimHei", color=BLACK).scale(0.6)
        black_coating_label.next_to(normal_tongue_copy, DOWN, buff=0.5)  # 增加间距，适应更大的舌头
        
        # 灰黑苔描述
        black_coating_desc = Text(
            "灰黑苔：正常舌苔由白色变为灰黑色，提示热极或寒极", 
            font="SimHei", 
            color=BLACK
        ).scale(0.5)
        black_coating_desc.to_edge(RIGHT, buff=1.2).shift(DOWN * 0.5)  # 增加右边距，适应更大的舌头
        
        # 创建灰黑苔
        black_coating = yellow_coating.copy()
        black_coating.set_fill(rgb_to_color([0.3, 0.3, 0.3]), opacity=0.5)  # 灰黑色
        
        # 动画展示：黄苔变为灰黑苔
        self.play(
            FadeOut(yellow_coating),
            FadeOut(yellow_coating_label),
            FadeOut(yellow_coating_desc),
            FadeIn(black_coating),
            Write(black_coating_label),
            Write(black_coating_desc)
        )
        
        self.wait(2)
        
        # 恢复正常舌象
        self.play(
            FadeOut(normal_tongue_body),
            FadeOut(black_coating),
            FadeOut(black_coating_label),
            FadeOut(black_coating_desc),
            FadeOut(title),
            FadeIn(normal_tongue)
        )
    
    def create_abnormal_tongue_from_normal(self, color, coating_color, coating_opacity=0.3, width_scale=1.0, height_scale=1.0, coating_scale=1.0):
        """基于正常舌象创建异常舌象"""
        # 创建正常舌象的各个部分
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        
        # 修改舌体颜色
        tongue_body.set_fill(color, opacity=1)
        
        # 修改舌苔颜色和透明度
        tongue_coating.set_fill(coating_color, opacity=coating_opacity)
        
        # 调整大小
        tongue_body.scale(width_scale, height_scale)
        tongue_coating.scale(width_scale * coating_scale, height_scale * coating_scale)
        
        # 组合在一起（不包含舌沟，因为某些异常舌象可能不需要显示舌沟）
        abnormal_tongue = VGroup(tongue_body, tongue_coating)
        abnormal_tongue.move_to(ORIGIN)
        
        return abnormal_tongue
    
    def create_teeth_mark_tongue(self):
        """创建齿痕舌"""
        # 基础舌体
        tongue = self.create_abnormal_tongue_from_normal(
            color=rgb_to_color([0.9, 0.6, 0.6]),
            coating_color=rgb_to_color([0.95, 0.95, 0.95]),
            coating_opacity=0.3,
            width_scale=1.1,
            height_scale=1.1
        )
        
        # 添加齿痕（左右两侧的小凹陷）
        left_mark1 = Line(LEFT * 0.6 + UP * 0.2, LEFT * 0.6 + DOWN * 0.2, color=BLACK)
        left_mark2 = Line(LEFT * 0.8 + UP * 0.15, LEFT * 0.8 + DOWN * 0.15, color=BLACK)
        right_mark1 = Line(RIGHT * 0.6 + UP * 0.2, RIGHT * 0.6 + DOWN * 0.2, color=BLACK)
        right_mark2 = Line(RIGHT * 0.8 + UP * 0.15, RIGHT * 0.8 + DOWN * 0.15, color=BLACK)
        
        teeth_marks = VGroup(left_mark1, left_mark2, right_mark1, right_mark2)
        
        return VGroup(tongue, teeth_marks)
    
    def create_cracked_tongue(self):
        """创建裂纹舌"""
        # 基础舌体
        tongue = self.create_abnormal_tongue_from_normal(
            color=rgb_to_color([0.9, 0.5, 0.5]),  # 略深的红色
            coating_color=rgb_to_color([0.95, 0.95, 0.95]),
            coating_opacity=0.2
        )
        
        # 添加裂纹
        center_crack = Line(DOWN * 0.3, UP * 0.3, color=BLACK)
        left_crack = Line(LEFT * 0.4 + DOWN * 0.1, LEFT * 0.4 + UP * 0.2, color=BLACK).rotate(PI/6)
        right_crack = Line(RIGHT * 0.4 + DOWN * 0.1, RIGHT * 0.4 + UP * 0.2, color=BLACK).rotate(-PI/6)
        
        cracks = VGroup(center_crack, left_crack, right_crack)
        
        return VGroup(tongue, cracks)


class TongueSceneManager(Scene):
    """舌诊动画场景管理类，用于按顺序播放舌的部位解析场景和舌诊动画场景"""
    
    def construct(self):
        # 首先播放舌的部位解析场景
        analysis_scene = TongueAnalysisScene()
        analysis_scene.construct()
        
        # 然后播放舌诊动画场景
        animation_scene = TongueAnimation()
        animation_scene.construct()
