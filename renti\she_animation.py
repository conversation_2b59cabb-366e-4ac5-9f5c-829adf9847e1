from manim import *
import numpy as np

class CompleteTongueScene(Scene):
    """完整的舌诊教学场景，包含部位介绍、观察维度、正常舌象和异常舌象"""

    def construct(self):
        # 设置背景为白色
        self.camera.background_color = "#FFFFFF"

        # 第一部分：舌部位解析与观察维度介绍
        self.show_tongue_analysis()

        # 第二部分：正常舌象展示
        self.show_normal_tongue()

        # 第三部分：异常舌象展示
        self.show_abnormal_tongues()

        # 结束
        self.show_conclusion()

    def show_tongue_analysis(self):
        """第一部分：舌部位解析与观察维度介绍"""
        # 创建主标题
        main_title = Text("中医舌诊", font="SimHei", color=BLACK).scale(1.2)
        main_title.to_edge(UP, buff=0.3)
        self.play(Write(main_title))
        self.wait(1)

        # 创建第一部分标题
        part1_title = Text("第一部分：舌部位解析与观察维度", font="SimHei", color=BLUE).scale(0.8)
        part1_title.next_to(main_title, DOWN, buff=0.5)
        self.play(Write(part1_title))
        self.wait(1)

        # 创建舌头的各个部分 - 调整位置使其更居中
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        tongue_group = VGroup(tongue_body, tongue_coating)
        tongue_group.scale(0.8).move_to(ORIGIN)

        # 不使用文本区域背景，所有解释文字直接显示在靠近解释对象的地方

        # 首先同时显示舌质和舌苔
        self.play(FadeIn(tongue_group))
        self.wait(1)
        
        # 舌头整体标签
        tongue_label = Text("舌头整体", font="SimHei", color=BLACK).scale(0.7)
        tongue_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(tongue_label))
        self.wait(1)
        
        # 动画左右分离舌质和舌苔
        # 创建舌质和舌苔的副本用于分离展示
        tongue_body_left = tongue_body.copy()
        tongue_coating_right = tongue_coating.copy()
        
        # 设置目标位置
        tongue_body_left.generate_target()
        tongue_coating_right.generate_target()
        tongue_body_left.target.move_to(LEFT * 3)
        tongue_coating_right.target.move_to(RIGHT * 1)
        
        # 执行分离动画
        self.play(
            FadeOut(tongue_group),
            FadeOut(tongue_label),
            MoveToTarget(tongue_body_left),
            MoveToTarget(tongue_coating_right)
        )
        
        # 添加舌质标签和说明
        tongue_body_label = Text("舌质", font="SimHei", color=BLACK).scale(0.7)
        tongue_body_label.next_to(tongue_body_left, DOWN, buff=0.5)
        
        # 舌质说明文本
        tongue_body_text = Text(
            "舌头的肉质部分",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        tongue_body_text.next_to(tongue_body_label, DOWN, buff=0.5)
        
        
        # 添加舌苔标签和说明
        tongue_coating_label = Text("舌苔", font="SimHei", color=BLACK).scale(0.7)
        tongue_coating_label.next_to(tongue_body_label, RIGHT, buff=3.2)
        
        # 舌苔说明文本
        tongue_coating_text = Text(
            "舌头表面的薄薄一层白苔",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        tongue_coating_text.next_to(tongue_coating_label, DOWN, buff=0.5)
        
       
        
        # 依次显示舌质和舌苔的标签和说明
        # 先显示舌质的标签和说明
        self.play(Write(tongue_body_label))
        self.wait(0.5)
        self.play(Write(tongue_body_text))
        self.wait(1)
        
        # 然后显示舌苔的标签和说明
        self.play(Write(tongue_coating_label))
        self.wait(0.5)
        self.play(Write(tongue_coating_text))
        self.wait(1)

        # 3. 舌质观察维度 - 先清除当前内容
        self.play(
            FadeOut(tongue_coating_right), 
            FadeOut(tongue_coating_label),
            FadeOut(tongue_coating_text), 
            FadeOut(tongue_body_text),
            FadeOut(tongue_body_label)
        )
        
        # 将舌质放大并移到中心
        tongue_body_enlarged = tongue_body_left.copy()
        tongue_body_enlarged.scale(1.5).move_to(ORIGIN)
        
        # 执行放大动画，使用Transform实现平滑过渡
        self.play(
            Transform(tongue_body_left, tongue_body_enlarged),
            FadeOut(tongue_body_label)
        )
        
        # 将tongue_body_left替换为tongue_body_enlarged，以便后续操作
        tongue_body_enlarged = tongue_body_left
        
        # 显示舌质观察维度标题
        tongue_dimensions_title = Text("舌质观察维度", font="SimHei", color=BLACK).scale(0.7)
        tongue_dimensions_title.next_to(tongue_body_enlarged, LEFT, buff=2).shift(UP * 1)
        self.play(Write(tongue_dimensions_title))
        
        # 创建维度列表
        dimensions = [
            Text("颜色", font="SimHei", color=RED).scale(0.6),
            Text("形状", font="SimHei", color=GREEN).scale(0.6),
            Text("润泽度", font="SimHei", color=BLUE).scale(0.6)
        ]
        
        # 设置维度列表位置
        for i, dim in enumerate(dimensions):
            dim.next_to(tongue_dimensions_title, DOWN, buff=0.5 + i * 0.8)
            
        # 显示维度列表
        self.play(*[Write(dim) for dim in dimensions])
        self.wait(1)
        
        # 1. 颜色维度演示
        # 复制颜色维度文本并放大
        color_focus = dimensions[0].copy()
        color_focus.generate_target()
        color_focus.target.scale(1.5).next_to(tongue_body_enlarged, DOWN, buff=1)
        
        # 移动颜色维度到舌质下方并放大
        self.play(MoveToTarget(color_focus))
        self.wait(0.5)
        
        # 颜色变化演示
        colors = [
            rgb_to_color([0.95, 0.85, 0.85]),  # 淡白
            rgb_to_color([0.9, 0.3, 0.3]),     # 红色
            rgb_to_color([0.6, 0.3, 0.6]),     # 紫色
            rgb_to_color([0.9, 0.5, 0.5])      # 恢复正常
        ]
        
        # 执行颜色变化动画
        for i, color in enumerate(colors):
            self.play(
                tongue_body_enlarged.animate.set_color(color),
                run_time=0.8
            )
            self.wait(0.5)
        
        # 清除颜色焦点
        color_focus.generate_target()
        color_focus.target.scale(1/1.5).move_to(dimensions[0])
        self.play(MoveToTarget(color_focus))
        self.wait(0.5)
        
        # 2. 形状维度演示
        # 复制形状维度文本并放大
        shape_focus = dimensions[1].copy()
        shape_focus.generate_target()
        shape_focus.target.scale(1.5).next_to(tongue_body_enlarged, DOWN, buff=1)
        
        # 移动形状维度到舌质下方并放大
        self.play(MoveToTarget(shape_focus))
        self.wait(0.5)
        
        # 形状变化演示
        # 保存原始舌质
        original_tongue = tongue_body_enlarged.copy()
        
        # 胖大舌
        self.play(
            tongue_body_enlarged.animate.scale(1.2)
        )
        self.wait(1)
        
        # 瘦小舌
        self.play(
            tongue_body_enlarged.animate.scale(0.7)
        )
        self.wait(1)
        
        # 恢复原始大小
        normal_tongue = original_tongue.copy()
        self.play(
            Transform(tongue_body_enlarged, normal_tongue)
        )
        self.wait(0.5)
        
        # 歪舌（根部不动，中前部偏向一侧）
        crooked_tongue = original_tongue.copy()
        
        # 创建一个变形矩阵，使舌头前部偏向右侧
        # 这种变形会保持舌根部不变，而使前部偏向一侧
        points = crooked_tongue.points
        # 找到舌头的中心点
        center_x = np.mean(points[:, 0])
        center_y = np.mean(points[:, 1])
        
        # 对每个点进行变形
        for i in range(len(points)):
            # 获取点的坐标
            x, y = points[i, 0], points[i, 1]
            
            # 计算与舌根部的距离（假设舌根在下方）
            # 距离越远，变形越大
            distance_from_root = (y - center_y + 0.8) / 1.6  # 标准化为0-1范围
            
            # 只对上半部分进行变形（距离舌根较远的部分）
            if distance_from_root > 0.5:  # 只对上半部分变形
                # 变形强度与距离成正比
                shift_amount = (distance_from_root - 0.5) * 0.8
                # 向右偏移
                points[i, 0] += shift_amount
        
        # 更新舌头的点
        crooked_tongue.points = points
        
        self.play(
            Transform(normal_tongue, crooked_tongue)
        )
        self.wait(1)
        
        # 裂舌
        cracked_tongue = original_tongue.copy()
        # 添加更明显的中间裂缝
        cracks = VGroup()
        # 主裂缝
        main_crack = Line(
            start=np.array([0, 0.5, 0]),
            end=np.array([0, -1.0, 0]),
            color=BLACK,
            stroke_width=3
        )
        # 添加分支裂缝
        branch1 = Line(
            start=np.array([0, -0.3, 0]),
            end=np.array([0.3, -0.5, 0]),
            color=BLACK,
            stroke_width=2
        )
        branch2 = Line(
            start=np.array([0, -0.6, 0]),
            end=np.array([-0.3, -0.8, 0]),
            color=BLACK,
            stroke_width=2
        )
        cracks.add(main_crack, branch1, branch2)
        cracked_tongue.add(cracks)
        
        self.play(
            Transform(normal_tongue, cracked_tongue)
        )
        self.wait(1)
        
        # 恢复正常舌质
        final_tongue = original_tongue.copy()
        self.play(
            Transform(normal_tongue, final_tongue)
        )
        self.wait(1)
        
        # 形状焦点变形回原位置
        shape_focus.generate_target()
        shape_focus.target.scale(1/1.5).move_to(dimensions[1])
        self.play(
            MoveToTarget(shape_focus),
            FadeOut(final_tongue)
        )
        self.wait(0.5)
        
        # 3. 润泽度维度演示
        # 复制润泽度维度文本并放大
        moisture_focus = dimensions[2].copy()
        moisture_focus.generate_target()
        moisture_focus.target.scale(1.5).next_to(tongue_body_enlarged, DOWN, buff=1)
        
        # 移动润泽度维度到舌质下方并放大
        self.play(MoveToTarget(moisture_focus))
        self.wait(1)
        
        # 润泽度变化演示
        # 干燥舌
        dry_tongue = original_tongue.copy()
        dry_tongue.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=0.9)
        
        # 添加干燥纹理
        dry_lines = VGroup()
        for i in range(12):  # 增加裂纹数量
            angle = i * PI / 6
            line = Line(
                start=ORIGIN,
                end=np.array([np.cos(angle), np.sin(angle), 0]) * 0.8,
                color=rgb_to_color([0.8, 0.4, 0.4]),
                stroke_width=1.5  # 增加线条粗细
            )
            dry_lines.add(line)
        
        # 添加一些小裂纹
        for _ in range(8):
            start_point = np.array([np.random.uniform(-0.7, 0.7), np.random.uniform(-0.7, 0.7), 0])
            end_point = start_point + np.array([np.random.uniform(-0.2, 0.2), np.random.uniform(-0.2, 0.2), 0])
            small_crack = Line(
                start=start_point,
                end=end_point,
                color=rgb_to_color([0.8, 0.4, 0.4]),
                stroke_width=1
            )
            dry_lines.add(small_crack)
            
        dry_tongue.add(dry_lines)
        
        # 添加干燥粉尘效果
        dust_dots = VGroup()
        for _ in range(20):
            position = np.array([np.random.uniform(-0.8, 0.8), np.random.uniform(-0.8, 0.8), 0])
            dot = Dot(
                point=position,
                radius=0.02,
                color=rgb_to_color([0.85, 0.45, 0.45]),
                fill_opacity=0.8
            )
            dust_dots.add(dot)
        dry_tongue.add(dust_dots)
        
        # 只添加干燥指示文字
        dry_text = Text("干燥", font="SimHei", color=RED_E).scale(0.7)
        dry_text.move_to(dry_tongue.get_center() + UP * 1.5)
        
        self.play(
            Transform(original_tongue, dry_tongue)
        )
        self.wait(0.5)
        
        self.play(
            FadeIn(dry_text)
        )
        self.wait(1)
        
        # 清除干燥指示文字
        self.play(
            FadeOut(dry_text)
        )
        self.wait(0.3)
        
        # 恢复原始舌头，完全去除干燥裂纹效果
        # 创建一个全新的舌头，而不是复制已经变形的original_tongue
        clean_tongue = Ellipse(
            width=1.6, height=1.0,
            fill_color=rgb_to_color([0.9, 0.5, 0.5]),
            fill_opacity=0.95,
            stroke_color=rgb_to_color([0.85, 0.45, 0.45]),
            stroke_width=1
        ).move_to(original_tongue.get_center())
        
        self.play(
            Transform(original_tongue, clean_tongue)
        )
        self.wait(0.3)
        
        # 过度润泽舌
        moist_tongue = clean_tongue.copy()
        moist_tongue.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=1.0)
        
        # 添加多个光泽效果
        glosses = VGroup()
        # 主要光泽
        main_gloss = Ellipse(
            width=1.0, height=0.5,
            fill_color=WHITE, fill_opacity=0.4,
            stroke_width=0
        ).move_to(moist_tongue.get_center() + UP * 0.2)
        glosses.add(main_gloss)
        
        # 添加额外的小光泽
        for _ in range(5):
            position = np.array([np.random.uniform(-0.6, 0.6), np.random.uniform(-0.6, 0.6), 0])
            size = np.random.uniform(0.1, 0.3)
            small_gloss = Ellipse(
                width=size, height=size/2,
                fill_color=WHITE, fill_opacity=0.3,
                stroke_width=0
            ).move_to(position)
            glosses.add(small_gloss)
        
        # 添加水滴效果
        water_drops = VGroup()
        for _ in range(8):
            # 水滴位置，主要在舌头边缘
            angle = np.random.uniform(0, 2*PI)
            distance = np.random.uniform(0.7, 0.9)  # 距离中心的距离
            position = np.array([np.cos(angle) * distance, np.sin(angle) * distance, 0])
            
            # 创建水滴
            drop = Circle(
                radius=0.08,
                fill_color=BLUE_E,
                fill_opacity=0.6,
                stroke_color=BLUE,
                stroke_width=1,
                stroke_opacity=0.8
            ).move_to(position)
            
            # 添加水滴高光
            highlight = Circle(
                radius=0.03,
                fill_color=WHITE,
                fill_opacity=0.8,
                stroke_width=0
            ).move_to(position + UP * 0.03 + LEFT * 0.03)
            
            water_drops.add(drop, highlight)
        
        moist_tongue.add(glosses, water_drops)
        
        # 只添加润泽指示文字
        moist_text = Text("润泽", font="SimHei", color=BLUE_D).scale(0.7)
        moist_text.move_to(moist_tongue.get_center() + UP * 1.5)
        
        self.play(
            Transform(original_tongue, moist_tongue)
        )
        self.wait(0.5)
        
        self.play(
            FadeIn(moist_text)
        )
        self.wait(1)
        
        # 清除润泽指示文字
        self.play(
            FadeOut(moist_text)
        )
        
        # 恢复正常
        normal_tongue = original_tongue.copy()
        normal_tongue.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=0.95)
        
        # 添加轻微光泽效果
        normal_gloss = Ellipse(
            width=0.8, height=0.4,
            fill_color=WHITE, fill_opacity=0.2,
            stroke_width=0
        ).move_to(normal_tongue.get_center() + UP * 0.1)
        normal_tongue.add(normal_gloss)
        
        self.play(
            Transform(original_tongue, normal_tongue)
        )
        self.wait(1)
        
        # 创建小舌质和舌苔的副本，用于变形回并列状态
        small_tongue_body = self.create_normal_tongue_parts()[0]
        small_tongue_coating = self.create_normal_tongue_parts()[1]
        
        # 设置小舌质和舌苔的位置
        small_tongue_body.scale(0.8).move_to(LEFT * 3)
        small_tongue_coating.scale(0.8).move_to(RIGHT * 1)
        
        # 润泽度焦点变形回原位置
        moisture_focus.generate_target()
        moisture_focus.target.scale(1/1.5).move_to(dimensions[2])
        
        # 清除所有内容，完全回到舌质演示前的状态
        # 创建一个包含所有需要清除的元素的列表
        elements_to_clear = [
            moisture_focus,  # 润泽度焦点
            color_focus,    # 颜色焦点
            shape_focus,    # 形状焦点
            # original_tongue,  # 中间的舌质
            normal_tongue,  # 恢复正常的舌质
            tongue_dimensions_title,  # 舌质观察维度标题
        ]
        # 添加维度列表
        elements_to_clear.extend(dimensions)
        
        # 清除所有元素
        self.play(*[FadeOut(elem) for elem in elements_to_clear if elem in self.mobjects])
        self.wait(0.8)  # 等待一会儿
        
        # 创建舌苔的副本用于分离展示
        tongue_coating_right = self.create_normal_tongue_parts()[1]
        
        # 中间舌质变形回左侧的舌质
        tongue_body_left = self.create_normal_tongue_parts()[0]
        tongue_body_left.scale(0.8).move_to(LEFT * 3)
        
        # 将中间的舌质变形回左侧
        self.play(
            Transform(original_tongue, tongue_body_left)
        )
        self.wait(0.3)
        
        # 设置舌苔位置
        tongue_coating_right.scale(0.8).move_to(RIGHT * 1)
        
        # 添加标签
        tongue_body_label = Text("舌质", font="SimHei", color=BLACK).scale(0.7)
        tongue_body_label.next_to(tongue_body_left, DOWN, buff=0.5)
        
        tongue_coating_label = Text("舌苔", font="SimHei", color=BLACK).scale(0.7)
        tongue_coating_label.next_to(tongue_coating_right, DOWN, buff=0.5)
        
        # 显示舌苔和标签
        self.play(
            FadeIn(tongue_coating_right),
            FadeIn(tongue_body_label),
            FadeIn(tongue_coating_label)
        )
        self.wait(0.5)

        # 4. 舌苔观察维度 - 恢复到舌质和舌苔的分离展示
        # 创建第四部分标题
        coating_title = Text("第四部分：舌苔观察维度", font="SimHei", color=BLUE).scale(0.8)
        coating_title.to_edge(UP, buff=1.5)
        self.play(Write(coating_title))
        self.wait(0.5)
        
        # 将舌苔放大并移到中心
        tongue_coating_enlarged = tongue_coating_right.copy()
        tongue_coating_enlarged.scale(1.5).move_to(ORIGIN)
        
        # 执行放大动画，使用Transform实现平滑过渡
        self.play(
            FadeOut(tongue_body_left),  # 消除左侧的舌质
            FadeOut(tongue_body_label),
            Transform(tongue_coating_right, tongue_coating_enlarged),
            FadeOut(tongue_coating_label)
        )
        self.wait(0.5)
        
        # 将tongue_coating_right替换为tongue_coating_enlarged，以便后续操作
        tongue_coating_enlarged = tongue_coating_right
        
        # 显示舌苔观察维度标题
        coating_dimensions_title = Text("舌苔观察维度", font="SimHei", color=BLACK).scale(0.7)
        coating_dimensions_title.next_to(tongue_coating_enlarged, LEFT, buff=2).shift(UP * 1)
        self.play(Write(coating_dimensions_title))
        
        # 创建维度列表
        coating_dimensions = [
            Text("颜色", font="SimHei", color=RED).scale(0.6),
            Text("厚度", font="SimHei", color=GREEN).scale(0.6),
            Text("质地", font="SimHei", color=BLUE).scale(0.6)
        ]
        
        # 设置维度列表位置
        for i, dim in enumerate(coating_dimensions):
            dim.next_to(coating_dimensions_title, DOWN, buff=0.5 + i * 0.8)
            
        # 显示维度列表
        self.play(*[Write(dim) for dim in coating_dimensions])
        self.wait(1)
        
        # 1. 颜色维度演示
        # 复制颜色维度文本并放大
        color_focus = coating_dimensions[0].copy()
        color_focus.generate_target()
        color_focus.target.scale(1.5).next_to(tongue_coating_enlarged, DOWN, buff=0.5)
        
        # 移动颜色维度到舌苔下方并放大
        self.play(MoveToTarget(color_focus))
        self.wait(0.5)
        
        # 颜色变化演示
        # 保存原始舌苔
        original_coating = tongue_coating_enlarged.copy()
        
        # 准备不同颜色的舌苔
        coating_colors = [
            WHITE,  # 白苔（原始颜色）
            rgb_to_color([0.95, 0.9, 0.6]),  # 黄苔
            rgb_to_color([0.3, 0.3, 0.3]),  # 灰黑苔
            WHITE  # 恢复正常
        ]
        
        # 执行颜色变化动画
        for i, color in enumerate(coating_colors):
            if i > 0:  # 第一种颜色已经是原始颜色，不需要变化
                self.play(
                    tongue_coating_enlarged.animate.set_fill(color, opacity=0.4),
                    run_time=0.8
                )
            self.wait(0.5)
        
        # 清除颜色焦点
        color_focus.generate_target()
        color_focus.target.scale(1/1.5).move_to(dimensions[0])
        self.play(MoveToTarget(color_focus))
        self.wait(0.5)
        
        # 2. 厚度维度演示
        # 复制厚度维度文本并放大
        thickness_focus = coating_dimensions[1].copy()
        thickness_focus.generate_target()
        thickness_focus.target.scale(1.5).next_to(tongue_coating_enlarged, DOWN, buff=1.5)
        
        # 移动厚度维度到舌苔下方并放大
        self.play(MoveToTarget(thickness_focus))
        self.wait(0.5)
        
        # 厚度变化演示
        # 准备不同厚度的舌苔
        thin_coating = tongue_coating_enlarged.copy()  # 原始薄苔
        
        # 创建厚苔效果
        thick_coating = tongue_coating_enlarged.copy()
        thick_coating.set_fill(opacity=0.8)  # 增加不透明度
        
        # 添加可见的厚度指示器
        thickness_indicator = Rectangle(
            width=0.3, height=0.1,
            fill_color=WHITE, fill_opacity=1,
            stroke_width=0
        ).move_to(thick_coating.get_center() + UP * 0.2)
        thick_coating.add(thickness_indicator)
        
        # 执行厚度变化动画
        self.play(
            FadeOut(tongue_coating_enlarged),
            FadeIn(thick_coating),
            run_time=0.8
        )
        self.wait(0.8)
        
        # 恢复正常
        self.play(
            FadeOut(thick_coating),
            FadeIn(original_coating),
            run_time=0.8
        )
        self.wait(0.5)
        
        # 清除厚度焦点
        self.play(FadeOut(thickness_focus))
        self.wait(0.5)
        
        # 3. 质地维度演示
        # 复制质地维度文本并放大
        texture_focus = coating_dimensions[2].copy()
        texture_focus.generate_target()
        texture_focus.target.scale(1.5).next_to(tongue_coating_enlarged, DOWN, buff=0.5)
        
        # 移动质地维度到舌苔下方并放大
        self.play(MoveToTarget(texture_focus))
        self.wait(0.5)
        
        # 质地变化演示
        # 准备不同质地的舌苔
        
        # 1. 润苔（正常）
        moist_coating = original_coating.copy()
        
        # 2. 腔苔
        greasy_coating = original_coating.copy()
        # 添加腔效果（润泽感增强）
        gloss = Ellipse(
            width=1.0, height=0.5,
            fill_color=WHITE, fill_opacity=0.4,
            stroke_width=0
        ).move_to(greasy_coating.get_center() + UP * 0.2)
        greasy_coating.add(gloss)
        
        # 3. 燥苔
        dry_coating = original_coating.copy()
        # 添加干燥效果（裂纹）
        cracks = VGroup()
        for i in range(5):
            crack = Line(
                start=np.array([-0.5 + 0.25*i, 0.5, 0]),
                end=np.array([-0.3 + 0.25*i, -0.5, 0]),
                color=BLACK,
                stroke_width=1
            )
            cracks.add(crack)
        dry_coating.add(cracks)
        
        # 4. 剥落苔
        peeled_coating = original_coating.copy()
        # 创建剥落效果（只覆盖部分舌面）
        peeled_coating.set_fill(opacity=0.1)  # 减少不透明度
        # 添加局部覆盖的苔块
        patches = VGroup()
        for _ in range(3):
            x = np.random.uniform(-0.3, 0.3)
            y = np.random.uniform(-0.3, 0.3)
            patch = Ellipse(
                width=np.random.uniform(0.1, 0.3),
                height=np.random.uniform(0.1, 0.2),
                fill_color=WHITE,
                fill_opacity=0.4,
                stroke_width=0
            ).move_to(np.array([x, y, 0]))
            patches.add(patch)
        peeled_coating.add(patches)
        
        # 执行质地变化动画
        coatings = [moist_coating, greasy_coating, dry_coating, peeled_coating, moist_coating]
        
        # 显示第一种质地（润苔）
        self.play(FadeIn(moist_coating))
        self.wait(0.5)
        
        # 依次展示不同质地
        for i in range(1, len(coatings)):
            self.play(
                FadeOut(coatings[i-1]),
                FadeIn(coatings[i]),
                run_time=0.8
            )
            self.wait(0.5)
            
        # 清除质地相关内容
        self.play(
            FadeOut(texture_focus),
            FadeOut(coatings[-1]),
            FadeOut(coating_dimensions_title),
            *[FadeOut(dim) for dim in coating_dimensions]
        )
        
        # 清理第一部分
        self.play(
            FadeOut(part1_title)
        )

        # 保存主标题供后续使用
        self.main_title = main_title

    def demonstrate_tongue_dimensions(self, tongue_body):
        """演示舌质观察维度变化（简化版本）"""
        # 颜色变化演示
        color_demo = Text("→ 颜色变化", font="SimHei", color=RED).scale(0.6)
        color_demo.next_to(tongue_body, UP, buff=0.5)
        self.play(FadeIn(color_demo))

        # 快速演示颜色变化
        colors = [
            rgb_to_color([0.95, 0.85, 0.85]),  # 淡白
            rgb_to_color([0.9, 0.3, 0.3]),     # 红色
            rgb_to_color([0.9, 0.5, 0.5])      # 恢复正常
        ]
        for color in colors:
            self.play(tongue_body.animate.set_color(color), run_time=0.8)
            self.wait(0.5)

        self.play(FadeOut(color_demo))

    def demonstrate_coating_dimensions(self, tongue_coating):
        """演示舌苔观察维度变化（简化版本，重点改善厚度表现）"""
        # 厚度变化演示 - 改善表现效果
        thickness_demo = Text("→ 厚度变化", font="SimHei", color=BLUE).scale(0.6)
        thickness_demo.next_to(tongue_coating, UP, buff=0.5)
        self.play(FadeIn(thickness_demo))

        # 创建厚度指示器
        thickness_indicator = Rectangle(
            width=0.3, height=0.05,
            fill_color=WHITE, fill_opacity=1,
            stroke_color=BLACK, stroke_width=2
        )
        thickness_indicator.next_to(tongue_coating, RIGHT, buff=0.5)

        # 薄苔标签
        thin_label = Text("薄苔", font="SimHei", color=BLACK).scale(0.4)
        thin_label.next_to(thickness_indicator, RIGHT, buff=0.2)

        self.play(FadeIn(thickness_indicator), FadeIn(thin_label))
        self.wait(1)

        # 变为厚苔 - 使用多层效果和厚度指示器
        thick_coating = tongue_coating.copy()
        thick_coating.set_fill(rgb_to_color([0.9, 0.9, 0.9]), opacity=0.8)
        thick_coating.scale(1.1)

        # 厚苔指示器
        thick_indicator = Rectangle(
            width=0.3, height=0.15,  # 增加高度表示厚度
            fill_color=WHITE, fill_opacity=1,
            stroke_color=BLACK, stroke_width=2
        )
        thick_indicator.next_to(tongue_coating, RIGHT, buff=0.5)

        thick_label = Text("厚苔", font="SimHei", color=BLACK).scale(0.4)
        thick_label.next_to(thick_indicator, RIGHT, buff=0.2)

        self.play(
            FadeOut(tongue_coating),
            FadeIn(thick_coating),
            Transform(thickness_indicator, thick_indicator),
            Transform(thin_label, thick_label)
        )
        self.wait(1)

        # 恢复薄苔
        self.play(
            FadeOut(thick_coating),
            FadeIn(tongue_coating),
            FadeOut(thickness_indicator),
            FadeOut(thin_label),
            FadeOut(thickness_demo)
        )

    def show_normal_tongue(self):
        """第二部分：正常舌象展示"""
        # 创建第二部分标题
        part2_title = Text("第二部分：正常舌象", font="SimHei", color=GREEN).scale(0.8)
        part2_title.next_to(self.main_title, DOWN, buff=0.5)
        self.play(Write(part2_title))
        self.wait(1)

        # 创建正常舌象的各个部分
        tongue_body, tongue_coating = self.create_normal_tongue_parts()
        tongue_group = VGroup(tongue_body, tongue_coating)
        tongue_group.scale(0.9).move_to(LEFT * 2.5)

        # 不再需要信息区域背景

        # 创建正常舌象标签
        normal_tongue_label = Text("正常舌象", font="SimHei", color=BLACK).scale(0.7)
        normal_tongue_label.next_to(tongue_group, DOWN, buff=0.8)
        self.play(Write(normal_tongue_label))

        # 1. 先显示舌质颜色
        self.play(FadeIn(tongue_body))

        # 显示舌质说明文本
        tongue_color_text = Text(
            "舌质淡红明润",
            font="SimHei",
            color=BLACK
        ).scale(0.6)
        tongue_color_text.next_to(tongue_body, RIGHT, buff=1.0)

        # 添加从文本到舌质的箭头
        color_arrow = Arrow(
            start=tongue_color_text.get_left() + LEFT * 0.2,
            end=tongue_body.get_center() + RIGHT * 0.5,
            color=RED,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_color_text), Create(color_arrow))
        self.wait(1.5)
        self.play(FadeOut(tongue_color_text), FadeOut(color_arrow))

        # 2. 显示舌苔
        self.play(FadeIn(tongue_coating))

        # 显示舌苔说明文本
        tongue_coating_text = Text(
            "舌苔薄白均匀",
            font="SimHei",
            color=BLACK
        ).scale(0.6)
        tongue_coating_text.next_to(tongue_coating, RIGHT, buff=1.0)

        # 添加从文本到舌苔的箭头
        coating_arrow = Arrow(
            start=tongue_coating_text.get_left() + LEFT * 0.2,
            end=tongue_coating.get_center() + RIGHT * 0.3,
            color=BLUE,
            buff=0.1,
            stroke_width=3
        )

        self.play(Write(tongue_coating_text), Create(coating_arrow))
        self.wait(1.5)
        self.play(FadeOut(tongue_coating_text), FadeOut(coating_arrow))

        # 显示完整说明文本
        complete_description = Text(
            "正常舌象特征：\n\n● 舌质淡红明润\n● 舌苔薄白均匀\n● 舌体大小适中\n● 边缘光滑无齿痕",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.55)
        complete_description.next_to(tongue_group, RIGHT, buff=1.0)

        self.play(Write(complete_description))
        self.wait(2)

        # 保存正常舌象组合供后续使用
        self.normal_tongue = VGroup(tongue_body, tongue_coating)
        self.normal_tongue_label = normal_tongue_label
        self.complete_description = complete_description
        # 不再需要保存info_bg
        self.part2_title = part2_title

    def show_abnormal_tongues(self):
        """第三部分：异常舌象展示"""
        # 创建第三部分标题
        part3_title = Text("第三部分：异常舌象", font="SimHei", color=RED).scale(0.8)
        part3_title.next_to(self.main_title, DOWN, buff=0.5)

        # 隐藏正常舌象的描述文本
        self.play(
            FadeOut(self.complete_description),
            FadeOut(self.normal_tongue_label),
            FadeOut(self.part2_title),
            Write(part3_title)
        )

        # 展示舌色异常
        self.show_color_abnormalities_complete(self.normal_tongue)

        # 展示舌形异常
        self.show_shape_abnormalities_complete(self.normal_tongue)

        # 展示舌苔异常（重点改善厚度和湿度表现）
        self.show_coating_abnormalities_complete(self.normal_tongue)

        # 保存第三部分标题
        self.part3_title = part3_title

    def show_color_abnormalities_complete(self, normal_tongue):
        """展示舌色异常（完整版，左右对比）"""
        # 子标题
        subtitle = Text("舌色异常", font="SimHei", color=BLACK).scale(0.7)
        subtitle.next_to(self.main_title, DOWN, buff=1.2)
        self.play(Write(subtitle))

        # 创建左右对比布局
        # 左侧：正常舌象（作为对比参考）
        normal_reference = normal_tongue.copy()
        normal_reference.scale(0.7).move_to(LEFT * 4)

        normal_label = Text("正常舌象", font="SimHei", color=GREEN).scale(0.5)
        normal_label.next_to(normal_reference, DOWN, buff=0.5)

        # 右侧：异常舌象（用于演示变化）
        abnormal_tongue_body = normal_tongue[0].copy()
        abnormal_tongue_body.scale(0.7).move_to(LEFT * 1)

        # 显示对比布局
        self.play(
            FadeIn(normal_reference),
            FadeIn(normal_label),
            FadeIn(abnormal_tongue_body)
        )

        # 演示淡白舌
        pale_tongue_label = Text("淡白舌", font="SimHei", color=BLACK).scale(0.5)
        pale_tongue_label.next_to(abnormal_tongue_body, DOWN, buff=0.5)

        pale_tongue_desc = Text(
            "淡白舌对比分析：\n\n● 正常舌质：淡红明润\n● 淡白舌质：色泽淡白\n● 临床意义：气血两虚，阳虚\n● 对比特点：颜色明显变淡",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.45)
        pale_tongue_desc.next_to(abnormal_tongue_body, RIGHT, buff=0.5)

        self.play(
            abnormal_tongue_body.animate.set_fill(rgb_to_color([0.95, 0.85, 0.85])),
            Write(pale_tongue_label),
            Write(pale_tongue_desc)
        )
        self.wait(3)

        # 演示红舌
        red_tongue_label = Text("红舌", font="SimHei", color=BLACK).scale(0.5)
        red_tongue_label.next_to(abnormal_tongue_body, DOWN, buff=0.5)

        red_tongue_desc = Text(
            "红舌对比分析：\n\n● 正常舌质：淡红明润\n● 红舌舌质：色泽鲜红\n● 临床意义：热证\n● 对比特点：颜色明显加深",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.45)
        red_tongue_desc.next_to(abnormal_tongue_body, RIGHT, buff=0.5)

        self.play(
            FadeOut(pale_tongue_label),
            FadeOut(pale_tongue_desc),
            abnormal_tongue_body.animate.set_fill(rgb_to_color([0.9, 0.3, 0.3])),
            Write(red_tongue_label),
            Write(red_tongue_desc)
        )
        self.wait(3)

        # 演示紫舌
        purple_tongue_label = Text("紫舌", font="SimHei", color=BLACK).scale(0.5)
        purple_tongue_label.next_to(abnormal_tongue_body, DOWN, buff=0.5)

        purple_tongue_desc = Text(
            "紫舌对比分析：\n\n● 正常舌质：淡红明润\n● 紫舌舌质：色泽紫暗\n● 临床意义：血瘀、寒凝\n● 对比特点：颜色偏紫偏暗",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.45)
        purple_tongue_desc.next_to(abnormal_tongue_body, RIGHT, buff=0.5)

        self.play(
            FadeOut(red_tongue_label),
            FadeOut(red_tongue_desc),
            abnormal_tongue_body.animate.set_fill(rgb_to_color([0.6, 0.3, 0.6])),
            Write(purple_tongue_label),
            Write(purple_tongue_desc)
        )
        self.wait(3)

        # 恢复正常并清理
        self.play(
            FadeOut(normal_reference),
            FadeOut(normal_label),
            FadeOut(abnormal_tongue_body),
            FadeOut(purple_tongue_label),
            FadeOut(purple_tongue_desc),
            FadeOut(subtitle),
            FadeIn(normal_tongue)
        )

    def show_shape_abnormalities_complete(self, normal_tongue):
        """展示舌形异常（完整版，左右对比）"""
        # 子标题
        subtitle = Text("舌形异常", font="SimHei", color=BLACK).scale(0.7)
        subtitle.next_to(self.main_title, DOWN, buff=1.2)
        self.play(Write(subtitle))

        # 创建左右对比布局
        # 左侧：正常舌象（作为对比参考）
        normal_reference = normal_tongue.copy()
        normal_reference.scale(0.7).move_to(LEFT * 4)

        normal_label = Text("正常舌象", font="SimHei", color=GREEN).scale(0.5)
        normal_label.next_to(normal_reference, DOWN, buff=0.5)

        # 右侧：异常舌象（用于演示变化）
        abnormal_tongue = normal_tongue.copy()
        abnormal_tongue.scale(0.7).move_to(LEFT * 1)

        # 显示对比布局
        self.play(
            FadeIn(normal_reference),
            FadeIn(normal_label),
            FadeIn(abnormal_tongue)
        )

        # 胖大舌演示
        fat_tongue_label = Text("胖大舌", font="SimHei", color=BLACK).scale(0.5)
        fat_tongue_label.next_to(abnormal_tongue, DOWN, buff=0.5)

        fat_tongue_desc = Text(
            "胖大舌对比分析：\n\n● 正常舌体：大小适中\n● 胖大舌体：明显胖大\n● 临床意义：脾肾阳虚\n● 对比特点：体积明显增大",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.45)
        fat_tongue_desc.next_to(abnormal_tongue, RIGHT, buff=0.5)

        self.play(
            abnormal_tongue.animate.scale(1.4),  # 在原有0.7基础上再放大
            Write(fat_tongue_label),
            Write(fat_tongue_desc)
        )
        self.wait(3)

        # 瘦小舌演示
        thin_tongue_label = Text("瘦小舌", font="SimHei", color=BLACK).scale(0.5)
        thin_tongue_label.next_to(abnormal_tongue, DOWN, buff=0.5)

        thin_tongue_desc = Text(
            "瘦小舌对比分析：\n\n● 正常舌体：大小适中\n● 瘦小舌体：明显瘦小\n● 临床意义：气血不足\n● 对比特点：体积明显缩小",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.45)
        thin_tongue_desc.next_to(abnormal_tongue, RIGHT, buff=0.5)

        self.play(
            FadeOut(fat_tongue_label),
            FadeOut(fat_tongue_desc),
            abnormal_tongue.animate.scale(0.5),  # 缩小到很小
            Write(thin_tongue_label),
            Write(thin_tongue_desc)
        )
        self.wait(3)

        # 恢复正常并清理
        self.play(
            FadeOut(normal_reference),
            FadeOut(normal_label),
            FadeOut(abnormal_tongue),
            FadeOut(thin_tongue_label),
            FadeOut(thin_tongue_desc),
            FadeOut(subtitle),
            FadeIn(normal_tongue)
        )

    def show_coating_abnormalities_complete(self, normal_tongue):
        """展示舌苔异常（完整版，左右对比，重点改善厚度和湿度表现）"""
        # 子标题
        subtitle = Text("舌苔异常", font="SimHei", color=BLACK).scale(0.7)
        subtitle.next_to(self.main_title, DOWN, buff=1.2)
        self.play(Write(subtitle))

        # 创建左右对比布局
        # 左侧：正常舌象（作为对比参考）
        normal_reference = normal_tongue.copy()
        normal_reference.scale(0.7).move_to(LEFT * 4)

        normal_label = Text("正常舌象", font="SimHei", color=GREEN).scale(0.5)
        normal_label.next_to(normal_reference, DOWN, buff=0.5)

        # 右侧：异常舌象（用于演示变化）
        abnormal_tongue = normal_tongue.copy()
        abnormal_tongue_body = abnormal_tongue[0]
        abnormal_tongue_coating = abnormal_tongue[1]
        abnormal_tongue.scale(0.7).move_to(LEFT * 1)

        # 显示对比布局
        self.play(
            FadeIn(normal_reference),
            FadeIn(normal_label),
            FadeIn(abnormal_tongue)
        )

        # 1. 厚腻苔演示 - 改善厚度表现
        thick_coating_label = Text("厚腻苔", font="SimHei", color=BLACK).scale(0.5)
        thick_coating_label.next_to(abnormal_tongue, DOWN, buff=0.5)

        thick_coating_desc = Text(
            "厚腻苔对比分析：\n\n● 正常舌苔：薄白均匀\n● 厚腻舌苔：厚腻不透\n● 临床意义：痰湿、食积\n● 对比特点：厚度明显增加",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.45)
        thick_coating_desc.move_to(info_bg.get_center())

        # 创建多层厚苔效果
        thick_coating_layer1 = abnormal_tongue_coating.copy()
        thick_coating_layer1.set_fill(rgb_to_color([0.9, 0.9, 0.9]), opacity=0.6)
        thick_coating_layer1.scale(1.1)

        thick_coating_layer2 = abnormal_tongue_coating.copy()
        thick_coating_layer2.set_fill(rgb_to_color([0.85, 0.85, 0.85]), opacity=0.4)
        thick_coating_layer2.scale(1.05)

        # 创建厚度指示器 - 3D效果
        thickness_base = Rectangle(
            width=0.3, height=0.06,
            fill_color=WHITE, fill_opacity=1,
            stroke_color=BLACK, stroke_width=2
        )
        thickness_top = Rectangle(
            width=0.25, height=0.06,
            fill_color=rgb_to_color([0.95, 0.95, 0.95]), fill_opacity=1,
            stroke_color=BLACK, stroke_width=1
        )
        thickness_top.next_to(thickness_base, UP, buff=0)
        thickness_indicator = VGroup(thickness_base, thickness_top)
        thickness_indicator.next_to(abnormal_tongue, RIGHT, buff=0.3)

        thick_label = Text("厚苔", font="SimHei", color=BLACK).scale(0.35)
        thick_label.next_to(thickness_indicator, RIGHT, buff=0.1)

        self.play(
            FadeOut(abnormal_tongue_coating),
            FadeIn(thick_coating_layer2),
            FadeIn(thick_coating_layer1),
            FadeIn(thickness_indicator),
            FadeIn(thick_label),
            Write(thick_coating_label),
            Write(thick_coating_desc)
        )
        self.wait(3)

        # 2. 干燥苔演示 - 改善湿度表现
        dry_coating_label = Text("干燥苔", font="SimHei", color=BLACK).scale(0.5)
        dry_coating_label.next_to(abnormal_tongue, DOWN, buff=0.5)

        dry_coating_desc = Text(
            "干燥苔对比分析：\n\n● 正常舌苔：润泽有津\n● 干燥舌苔：干燥无津\n● 临床意义：津液不足，热盛伤津\n● 对比特点：失去正常润泽",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.45)
        dry_coating_desc.move_to(info_bg.get_center())

        # 创建干燥效果 - 裂纹和粗糙纹理
        dry_coating = abnormal_tongue_coating.copy()
        dry_coating.set_fill(rgb_to_color([0.9, 0.9, 0.8]), opacity=0.8)  # 略黄的干燥色

        # 创建干燥裂纹 - 调整尺寸适应缩放后的舌头
        dry_cracks = VGroup()
        crack_positions = [
            (np.array([-0.2, 0.15, 0]), np.array([-0.05, -0.05, 0])),
            (np.array([0.05, 0.2, 0]), np.array([0.2, 0, 0])),
            (np.array([-0.15, -0.15, 0]), np.array([0, -0.25, 0])),
            (np.array([0.15, -0.05, 0]), np.array([0.25, -0.2, 0]))
        ]

        for start, end in crack_positions:
            crack = Line(start, end, color=rgb_to_color([0.7, 0.7, 0.6]), stroke_width=2)
            dry_cracks.add(crack)

        dry_cracks.move_to(abnormal_tongue_coating.get_center())

        # 创建湿度指示器
        moisture_indicator = VGroup()
        # 干燥状态 - 空的水滴
        dry_drop = Circle(radius=0.06, color=BLACK, stroke_width=2, fill_opacity=0)
        dry_drop.next_to(abnormal_tongue, RIGHT, buff=0.3)

        # 添加"X"表示无湿度
        cross_line1 = Line(LEFT * 0.04 + UP * 0.04, RIGHT * 0.04 + DOWN * 0.04, color=RED, stroke_width=2)
        cross_line2 = Line(LEFT * 0.04 + DOWN * 0.04, RIGHT * 0.04 + UP * 0.04, color=RED, stroke_width=2)
        cross_line1.move_to(dry_drop.get_center())
        cross_line2.move_to(dry_drop.get_center())

        moisture_indicator.add(dry_drop, cross_line1, cross_line2)

        dry_moisture_label = Text("干燥", font="SimHei", color=BLACK).scale(0.35)
        dry_moisture_label.next_to(moisture_indicator, RIGHT, buff=0.1)

        self.play(
            FadeOut(thick_coating_layer1),
            FadeOut(thick_coating_layer2),
            FadeOut(thickness_indicator),
            FadeOut(thick_label),
            FadeOut(thick_coating_label),
            FadeOut(thick_coating_desc),
            FadeIn(dry_coating),
            FadeIn(dry_cracks),
            FadeIn(moisture_indicator),
            FadeIn(dry_moisture_label),
            Write(dry_coating_label),
            Write(dry_coating_desc)
        )
        self.wait(3)

        # 恢复正常并清理
        self.play(
            FadeOut(normal_reference),
            FadeOut(normal_label),
            FadeOut(abnormal_tongue_body),
            FadeOut(dry_coating),
            FadeOut(dry_cracks),
            FadeOut(moisture_indicator),
            FadeOut(dry_moisture_label),
            FadeOut(dry_coating_label),
            FadeOut(dry_coating_desc),
            FadeOut(subtitle),
            FadeIn(normal_tongue)
        )

    def show_conclusion(self):
        """结论部分"""
        # 创建结论标题
        conclusion_title = Text("总结", font="SimHei", color=PURPLE).scale(0.8)
        conclusion_title.next_to(self.main_title, DOWN, buff=0.5)

        self.play(
            FadeOut(self.part3_title),
            Write(conclusion_title)
        )

        # 总结文本
        conclusion_text = Text(
            "舌诊是中医诊断的重要方法\n\n通过观察舌质和舌苔的变化\n可以了解人体内部的健康状况\n\n● 舌质反映脏腑气血状态\n● 舌苔反映胃肠功能状况\n● 综合分析有助于准确诊断",
            font="SimHei",
            color=BLACK,
            line_spacing=1.2
        ).scale(0.6)
        conclusion_text.move_to(self.info_bg.get_center())

        self.play(Write(conclusion_text))
        self.wait(3)

        # 最终淡出
        self.play(
            FadeOut(self.normal_tongue),
            FadeOut(conclusion_text),
            FadeOut(self.info_bg),
            FadeOut(conclusion_title),
            FadeOut(self.main_title)
        )

        # 结束感谢
        thanks_text = Text("谢谢观看！", font="SimHei", color=BLACK).scale(1.5)
        self.play(Write(thanks_text))
        self.wait(2)
        self.play(FadeOut(thanks_text))

    def create_normal_tongue_parts(self):
        """创建正常舌象的各个部分，便于逐步显示"""
        # 简化SVG路径，创建一个更简单的舌头轮廓
        tongue_outline = VMobject(color=BLACK, stroke_width=1.5)

        # 定义舌头轮廓的关键点
        # 根据SVG路径数据，舌头是一个长形，底部宽，顶部稍窄

        # 顶部点（舌根）
        top_left = np.array([-0.8, 0.8, 0])
        top_right = np.array([0.8, 0.8, 0])

        # 底部点（舌尖）
        bottom_left = np.array([-0.6, -1.2, 0])  # 舌尖更长
        bottom_right = np.array([0.6, -1.2, 0])

        # 使用贝塞尔曲线创建平滑的舌头轮廓

        # 左侧曲线 - 从舌根到舌尖的左侧
        left_curve = CubicBezier(
            start_anchor=top_left,
            start_handle=np.array([-1.0, 0.5, 0]),
            end_handle=np.array([-1.2, -0.5, 0]),
            end_anchor=bottom_left
        )

        # 底部曲线 - 舌尖部分
        bottom_curve = CubicBezier(
            start_anchor=bottom_left,
            start_handle=np.array([-0.3, -1.6, 0]),
            end_handle=np.array([0.3, -1.6, 0]),
            end_anchor=bottom_right
        )

        # 右侧曲线 - 从舌尖到舌根的右侧
        right_curve = CubicBezier(
            start_anchor=bottom_right,
            start_handle=np.array([1.2, -0.5, 0]),
            end_handle=np.array([1.0, 0.5, 0]),
            end_anchor=top_right
        )

        # 顶部曲线 - 舌根部分
        top_curve = CubicBezier(
            start_anchor=top_right,
            start_handle=np.array([0.5, 1.0, 0]),
            end_handle=np.array([-0.5, 1.0, 0]),
            end_anchor=top_left
        )

        # 组合所有曲线形成完整的舌头轮廓
        tongue_outline.append_points(left_curve.points)
        tongue_outline.append_points(bottom_curve.points)
        tongue_outline.append_points(right_curve.points)
        tongue_outline.append_points(top_curve.points)
        tongue_outline.close_path()

        # 填充舌体 - 粉红色
        tongue_body = tongue_outline.copy()
        tongue_body.set_fill(rgb_to_color([0.9, 0.5, 0.5]), opacity=1)  # 粉红色

        # 正常舌苔 - 薄白色并覆盖大部分舌面
        tongue_coating = tongue_outline.copy()
        tongue_coating.scale(0.85)  # 稍小一点的舌苔区域
        tongue_coating.set_fill(rgb_to_color([0.95, 0.95, 0.95]), opacity=0.2)  # 薄白色

        # 调整大小和位置
        tongue_body.scale(1.0).move_to(ORIGIN)
        tongue_coating.scale(1.0).move_to(ORIGIN)

        return tongue_body, tongue_coating
