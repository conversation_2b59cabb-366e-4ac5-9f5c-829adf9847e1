from manim import *
import numpy as np
import os

class AirInterception(Scene):
    def construct(self):
        # 添加背景音乐，在整个动画过程中播放
        # self.add_sound("assets/music/background_music.wav", gain=0.3)  # gain参数控制音量，设置为0.3以确保背景音乐不会盖过配音
        
        # 添加作者水印 - 使用固定位置确保可见
        author_watermark = Text("萤知月", font="SimSun", color=WHITE, opacity=0.5).scale(0.5)
        # 使用绝对坐标放置在右下角
        author_watermark.move_to(np.array([6, -3, 0]))
        # 确保水印始终在最上层
        self.add_foreground_mobject(author_watermark)
        
        # 播放开场配音 - 已注释
        # self.add_sound("assets/audio/intro.mp3")
        # self.wait(1)  # 等待一秒确保音频开始播放
        
        # 设置背景
        sky = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_color="#87CEEB",
            fill_opacity=1,
            stroke_width=0
        )
        
        ground = Rectangle(
            width=config.frame_width,
            height=config.frame_height/4,
            fill_color="#8B4513",
            fill_opacity=1,
            stroke_width=0
        ).move_to(DOWN * config.frame_height/3)
        
        self.add(sky, ground)
        
        # 创建字幕函数，只创建字幕对象而不显示
        def create_subtitle(text):
            subtitle = Text(text, font="SimSun", color=WHITE).scale(0.7)
            subtitle.to_edge(DOWN, buff=0.5)
            return subtitle  # 返回字幕对象，供后续使用
            
        # 播放红旗9BE配音 - 已注释
        # self.add_sound("assets/audio/hq9be.mp3")
        # self.wait(1)  # 等待一秒确保音频开始播放
        
        # 创建红旗9BE防空系统 (使用hq9_launcher.svg)
        hq16_launcher = SVGMobject("assets/hq9_launcher.svg", fill_opacity=1)
        hq16_launcher.scale(1.2)
        hq16_launcher.move_to(DOWN * 2 + RIGHT * 4)
        
        # 添加"红旗9BE"字样
        hq_label = Text("红旗9BE", font="SimSun", color=WHITE).scale(0.5)
        hq_label.next_to(hq16_launcher, DOWN, buff=0.3)  # 将文字放在发射车下方
        
        # 直接添加发射车和标签，不使用动画效果以减少等待时间
        self.add(hq16_launcher, hq_label)
        
        # 1. 阵风战机进入场景
        
        # 播放阵风战机配音 - 已注释
        # self.add_sound("assets/audio/rafale.mp3")
        # self.wait(1)  # 等待一秒确保音频开始播放
        
        # 加载阵风战机SVG
        rafale = SVGMobject("assets/rafale.svg", fill_opacity=1, fill_color=RED)
        # 调整大小和角度，机头朝右（飞行方向）
        rafale.scale(0.2).rotate(0)  # 将机头从朝上旋转为朝右
        rafale.move_to(LEFT * 6 + UP * 2)  # 初始位置
        
        # 添加"阵风"字样
        rafale_label = Text("阵风", font="SimSun", color=WHITE).scale(0.5)
        rafale_label.next_to(rafale, UP, buff=0.3)  # 将文字放在战机上方
        
        # 直接添加阵风战机和标签
        self.add(rafale, rafale_label)
        
        # 创建字幕
        subtitle1 = create_subtitle("印度阵风战机进攻巴基斯坦")
        
        # 阵风战机立即飞入场景，同时显示字幕
        self.play(
            FadeIn(subtitle1),
            rafale.animate.move_to(LEFT * 3 + UP * 2),
            FadeOut(rafale_label),  # 飞行过程中淡出标签
            rate_func=linear,
            run_time=2  # 减少飞行时间
        )
        
        # 2. 红旗16防空系统发现阵风战机
        
        # 播放雷达发现配音 - 已注释
        # self.add_sound("assets/audio/radar_detect.mp3")
        # self.wait(1)  # 等待一秒确保音频开始播放

        # 雷达波动画，直接从发射车发出
        # 获取发射车上方的位置作为雷达波起点
        radar_position = hq16_launcher.get_center() + UP * 0.5  # 从发射车上方发出雷达波
        
        # 计算从发射车到阵风飞机的方向和距离
        direction_vector = rafale.get_center() - radar_position
        distance = np.linalg.norm(direction_vector)  # 计算实际距离
        angle = np.arctan2(direction_vector[1], direction_vector[0])
        
        # 计算一个波从发射到消失需要的总时间
        wave_duration = 2  # 缩短波的持续时间为2秒
        wave_interval = wave_duration  # 每个波之间的间隔等于波的持续时间，确保均匀分布
        total_waves = 3  # 减少为3个波，表示捕捉到飞机
        
        # 创建所有波的动画
        all_animations = []
        for i in range(total_waves):
            # 创建初始雷达波
            start_wave = Arc(
                angle=PI/3,  # 60度的扇形（上下各30度）
                radius=0.1,  # 初始半径很小
                stroke_color=RED, 
                stroke_width=2, 
                stroke_opacity=1,
                start_angle=angle - PI/6,  # 让扇形中心对准阵风飞机（向下30度）
                arc_center=radar_position  # 明确指定圆心位置
            )
            
            # 创建中间状态的雷达波（到达阵风飞机位置时）
            mid_wave = Arc(
                angle=PI/3,  # 60度的扇形
                radius=distance,  # 刚好到达阵风飞机位置
                stroke_color=RED, 
                stroke_width=2, 
                stroke_opacity=1,  # 保持可见
                start_angle=angle - PI/6,
                arc_center=radar_position
            )
            
            # 创建最终状态的雷达波（刚好超过阵风飞机一点点）
            end_wave = Arc(
                angle=PI/3,  # 60度的扇形
                radius=distance * 1.1,  # 略微超过目标距离
                stroke_color=RED, 
                stroke_width=2, 
                stroke_opacity=0,  # 完全透明
                start_angle=angle - PI/6,
                arc_center=radar_position
            )
            
            # 创建这个波的完整动画序列
            # 使用两个独立的Transform动画，确保第一个完成后再执行第二个
            wave_animations = Succession(
                Transform(start_wave, mid_wave, run_time=wave_duration * 0.83, rate_func=linear),  # 占用总时间的83%
                Transform(start_wave, end_wave, run_time=wave_duration * 0.17, rate_func=linear),   # 占用总时间的17%
                lag_ratio=0  # 确保动画连续进行
            )
            
            # 将这个波的动画添加到总动画列表，设置适当的延迟
            all_animations.append(
                Succession(
                    Wait(i * wave_interval),  # 每个波延迟发射的时间
                    wave_animations
                )
            )
        
        # 同时播放所有波的动画
        self.play(
            AnimationGroup(
                *all_animations,
                lag_ratio=0  # 让所有动画同时开始
            ),
            run_time=(total_waves - 1) * wave_interval + wave_duration  # 总动画时间：最后一个波开始的时间加上一个波的持续时间
        )
        
        # 雷达锁定动画
        lock_line = DashedLine(
            radar_position,  # 使用之前定义的雷达位置变量
            rafale.get_center(),
            color=RED,
            dash_length=0.2
        )
        
        # 创建字幕
        subtitle2 = create_subtitle("阵风战机发现被雷达照射，开始掉头逃跑")
        
        # 同时显示雷达锁定线和字幕
        self.play(
            Create(lock_line),
            FadeOut(subtitle1),  # 字幕淡出
            FadeIn(subtitle2),
            run_time=1
        )
        
        # 创建逃跑路径，向右方逃跑（往回跑）
        escape_path = Arc(
            radius=2.5,
            angle=PI/2,  # 向右方转弯（往回跑）
            start_angle=PI,  # 从左开始
            stroke_opacity=0
        )
        escape_path.shift(rafale.get_center())
        
        # 同时执行飞机转向和字幕淡出
        self.play(
            # MoveAlongPath(rafale, escape_path),
            rafale.animate.scale([-1, 1, 1]),  # 水平翻转（x轴缩放因子为-1）
            FadeOut(subtitle2),  # 字幕淡出
            run_time=1
        )
        
        # 3. 信息传递给空警500
        
        # 创建字幕
        subtitle3 = create_subtitle("红旗9BE雷达将阵风飞机的信息传给ZDK-03预警机")
        
        # 创建 ZDK-03 预警机
        kj500 = SVGMobject("assets/预警机.svg", fill_opacity=1)
        
        # 调整大小和方向，预警机应该在空中左右巡逐
        kj500.scale(0.7).flip(UP)  # 机头朝左，表示巡逐
        kj500.move_to(RIGHT * 5 + UP * 3)  # 将预警机放在画面右上方，但不要太靠边
        
        # 添加"ZDK-03预警机"字样
        kj500_label = Text("ZDK-03预警机", font="SimSun", color=WHITE).scale(0.5)
        kj500_label.next_to(kj500, DOWN, buff=0.3)  # 将文字放在预警机上方
        
        # 在预警机上添加雷达圆盘
        radar_disk = Circle(radius=0.4, fill_color=LIGHT_GREY, fill_opacity=0.8, stroke_width=1)
        radar_disk.move_to(kj500.get_center())
        
        # 同时显示预警机、标签、雷达圆盘和字幕
        self.play(
            FadeIn(kj500), 
            FadeIn(kj500_label), 
            FadeIn(radar_disk),
            FadeIn(subtitle3),
            run_time=1
        )
        
        # 等待短暂后淡出标签和字幕
        self.wait(1)
        self.play(
            FadeOut(kj500_label),
            FadeOut(subtitle3),
            run_time=0.5
        )
        
        # 信息传递动画
        info_dot = Dot(color=YELLOW)
        info_dot.move_to(radar_position)
        
        self.play(
            info_dot.animate.move_to(kj500.get_center()),
            run_time=2
        )
        
        # 空警500雷达波 - 使用与红旗雷达相同的实现方法
        kj_radar_waves = VGroup()
        
        # 计算从空警500到阵风飞机的方向和距离
        direction_vector = rafale.get_center() - kj500.get_center()
        distance = np.linalg.norm(direction_vector)
        angle = np.arctan2(direction_vector[1], direction_vector[0])
        
        # 计算一个波从发射到消失需要的总时间
        wave_duration = 2  # 缩短波的持续时间为2秒
        wave_interval = wave_duration  # 每个波之间的间隔等于波的持续时间，确保均匀分布
        total_waves = 3  # 减少为3个波，表示捕捉到飞机
        
        # 创建所有波的动画
        all_animations = []
        for i in range(total_waves):
            # 创建初始雷达波
            start_wave = Arc(
                angle=PI/3,  # 60度的扇形（上下各30度）
                radius=0.1,  # 初始半径很小
                stroke_color=BLUE, 
                stroke_width=2, 
                stroke_opacity=1,
                start_angle=angle - PI/6,  # 让扇形中心对准阵风飞机
                arc_center=kj500.get_center()  # 明确指定圆心位置
            )
            
            # 创建中间状态的雷达波（到达阵风飞机位置时）
            mid_wave = Arc(
                angle=PI/3,  # 60度的扇形
                radius=distance,  # 刚好到达阵风飞机位置
                stroke_color=BLUE, 
                stroke_width=2, 
                stroke_opacity=1,  # 保持可见
                start_angle=angle - PI/6,
                arc_center=kj500.get_center()
            )
            
            # 创建最终状态的雷达波（刚好超过阵风飞机一点点）
            end_wave = Arc(
                angle=PI/3,  # 60度的扇形
                radius=distance * 1.1,  # 略微超过目标距离
                stroke_color=BLUE, 
                stroke_width=2, 
                stroke_opacity=0,  # 完全透明
                start_angle=angle - PI/6,
                arc_center=kj500.get_center()
            )
            
            # 创建这个波的完整动画序列
            wave_animations = Succession(
                Transform(start_wave, mid_wave, run_time=wave_duration * 0.83, rate_func=linear),  # 占用总时间的83%
                Transform(start_wave, end_wave, run_time=wave_duration * 0.17, rate_func=linear),   # 占用总时间的17%
                lag_ratio=0  # 确保动画连续进行
            )
            
            # 将这个波的动画添加到总动画列表，设置适当的延迟
            all_animations.append(
                Succession(
                    Wait(i * wave_interval),  # 每个波延迟发射的时间
                    wave_animations
                )
            )
        
        # 同时播放所有波的动画
        self.play(
            AnimationGroup(
                *all_animations,
                lag_ratio=0  # 让所有动画同时开始
            ),
            run_time=(total_waves - 1) * wave_interval + wave_duration  # 总动画时间：最后一个波开始的时间加上一个波的持续时间
        )
        
        # 添加一条直接连接到阵风飞机的雷达线
        radar_lock = DashedLine(
            kj500.get_center(),
            rafale.get_center(),
            color=BLUE,
            dash_length=0.2
        )
        kj_radar_waves.add(radar_lock)
        
        self.play(
            *[Create(wave) for wave in kj_radar_waves],
            run_time=2
        )
        
        # 淡出预警机到阵风战机的雷达锁定线，避免残留
        
# 淡出预警机到阵风战机的雷达锁定线，避免残留
        self.play(FadeOut(radar_lock))
        
# 4. 空警500指挥歼10
        
        # 创建字幕
        subtitle4 = create_subtitle("ZDK-03预警机将信息传给歼10CE战机")
        
        # 创建歼-10CE战机
        j10 = SVGMobject("assets/j10.svg", fill_opacity=1)
        # 调整大小和方向
        j10.scale(0.15)  # 减小缩放比例
        j10.flip(UP)  # 垂直镜像
        j10.move_to(RIGHT * 3 + UP * 2)  # 将歼10放在前方位置
                
        # 添加"歼-10CE"字样
        j10_label = Text("歼-10CE", font="SimSun", color=WHITE).scale(0.5)
        j10_label.next_to(j10, UP, buff=0.3)  # 将文字放在战机上方
        
        # 同时显示歼10战机、标签和字幕
        self.play(
            FadeIn(j10), 
            FadeIn(j10_label),
            FadeIn(subtitle4),
            run_time=1
        )
        
        # 等待短暂后淡出标签和字幕
        self.wait(1)
        self.play(
            FadeOut(j10_label),
            FadeOut(subtitle4),
            run_time=0.5
        )
        
        # 信息传递动画
        info_dot2 = Dot(color=YELLOW)
        info_dot2.move_to(kj500.get_center())
        
        self.play(
            info_dot2.animate.move_to(j10.get_center()),
            run_time=2
        )
        
        # 5. 歼10发射霹雳15导弹
        
        # 创建字幕
        subtitle5 = create_subtitle("歼10CE战机接受命令后，发射霹雳15E导弹")
        
        # 创建霹雳15导弹
        pl15 = SVGMobject("assets/空空导弹.svg", fill_opacity=1).scale(0.05)
        pl15.rotate(PI)  # 调整方向
        pl15.next_to(j10, DOWN, buff=0.1)
        
        # 添加"霹雳-15E"字样
        pl15_label = Text("霹雳-15E", font="SimSun", color=WHITE).scale(0.4)
        pl15_label.next_to(pl15, DOWN, buff=0.1)  # 将文字放在导弹上方
        
        # 同时显示导弹、标签和字幕
        self.play(
            FadeIn(pl15), 
            FadeIn(pl15_label),
            FadeIn(subtitle5),
            run_time=1
        )
        
        # 等待短暂后淡出标签和字幕
        self.wait(1)
        self.play(
            FadeOut(pl15_label),
            FadeOut(subtitle5),
            run_time=0.5
        )
        
        # 创建预警机指引线（在导弹发射前就建立连接）
        guidance_line = DashedLine(
            kj500.get_center(),
            pl15.get_center(),
            color=BLUE,
            dash_length=0.1
        )
        
        # 创建数据传输效果
        guidance_dots = VGroup(*[
            Dot(color=BLUE).scale(0.5)
            for _ in range(3)
        ])
        
        for dot in guidance_dots:
            dot.move_to(kj500.get_center())
        
        # 创建两条指引线，一条连接预警机和阵风战机，另一条连接预警机和导弹
        rafale_guidance_line = DashedLine(
            kj500.get_center(),
            rafale.get_center(),
            color=BLUE,
            dash_length=0.1
        )
        
        missile_guidance_line = DashedLine(
            kj500.get_center(),
            pl15.get_center(),
            color=YELLOW,
            dash_length=0.1
        )
        
        # 显示指引线 - 使用FadeIn而不是Create，避免可能的渲染问题
        self.play(FadeIn(rafale_guidance_line), FadeIn(missile_guidance_line))
        
        # 数据传输动画
        self.play(
            AnimationGroup(
                *[dot.animate.move_to(pl15.get_center()) for dot in guidance_dots],
                lag_ratio=0.3
            ),
            run_time=1
        )
        
        # 6. 导弹在预警机指引下发射
        
        # 创建字幕
        subtitle6 = create_subtitle("霹雳15E导弹在预警机的实时引导下追踪")
        
        # 显示字幕
        self.play(FadeIn(subtitle6), run_time=0.5)
        
        # 创建导弹发射路径
        missile_launch_path = Line(
            pl15.get_center(),
            LEFT * 2.5 + UP * 2,  # 向左前方发射
            stroke_opacity=0
        )
        
        # 创建函数来更新预警机到导弹的指引线（在发射过程中）
        def update_missile_launch_guidance(mob, alpha):
            # 计算导弹在发射路径上的当前位置
            current_missile_pos = missile_launch_path.point_from_proportion(alpha)
            # 更新指引线的终点为导弹的当前位置
            new_line = DashedLine(
                kj500.get_center(),
                current_missile_pos,
                color=YELLOW,  # 使用黄色区分导弹指引线
                dash_length=0.1
            )
            mob.become(new_line)
        

        # 阵风继续逃跑路径 - 向左逃跑
        escape_path2 = Line(
            rafale.get_center(),
            rafale.get_center() + LEFT * 2,  # 向左继续逃跑
            stroke_opacity=0
        )

        # 红旗9的锁定线消失
        self.play(FadeOut(lock_line))
        
        # 创建一个函数来更新阵风战机的指引线
        def update_rafale_guidance_line(mob, alpha):
            # 计算阵风战机在路径上的当前位置
            current_rafale_pos = escape_path2.point_from_proportion(alpha)
            # 更新指引线的终点为阵风战机的当前位置
            new_line = DashedLine(
                kj500.get_center(),
                current_rafale_pos,
                color=BLUE,
                dash_length=0.1
            )
            mob.become(new_line)
        
        # 同时播放阵风逃跑和导弹发射，并让两条指引线分别跟随阵风战机和导弹
        self.play(
            MoveAlongPath(rafale, escape_path2),
            MoveAlongPath(pl15, missile_launch_path),
            UpdateFromAlphaFunc(rafale_guidance_line, update_rafale_guidance_line),  # 阵风指引线跟随阵风
            UpdateFromAlphaFunc(missile_guidance_line, update_missile_launch_guidance),  # 导弹指引线跟随导弹
            FadeOut(subtitle6),  # 淡出字幕
            rate_func=rate_functions.ease_out_cubic,
            run_time=2
        )
        
        
        # 8. 导弹打开火控雷达追踪
        
        # 创建字幕对象
        subtitle7 = create_subtitle("霹雳导弹打开自身雷达，二次点火")
        
        # 导弹火控雷达波 - 扩大范围确保能覆盖到阵风战机
        missile_radar = VGroup()
        
        # 计算导弹到阵风战机的距离和方向
        missile_to_rafale_vector = rafale.get_center() - pl15.get_center()
        missile_to_rafale_distance = np.linalg.norm(missile_to_rafale_vector)
        angle = np.arctan2(missile_to_rafale_vector[1], missile_to_rafale_vector[0])
        
        # 创建指向阵风战机的扇形雷达波，确保能覆盖到目标
        for i in range(1, 4):
            # 使用扇形而非圆形，更符合火控雷达的特点
            wave = Arc(
                angle=PI/4,  # 45度的扇形
                radius=missile_to_rafale_distance * (0.5 + 0.25 * i),  # 覆盖距离的一半到全部
                stroke_color=RED, 
                stroke_width=2, 
                stroke_opacity=1/(i*0.8),
                start_angle=angle - PI/8,  # 让扇形中心对准阵风战机
                arc_center=pl15.get_center()  # 以导弹位置为圆心
            )
            missile_radar.add(wave)
        
        # 添加一条直接连接到阵风战机的雷达锁定线
        radar_lock = DashedLine(
            pl15.get_center(),
            rafale.get_center(),
            color=RED,
            dash_length=0.1
        )
        missile_radar.add(radar_lock)
        
        self.play(
            *[GrowFromCenter(wave) for wave in missile_radar[:-1]],  # 先创建扇形波
            Create(radar_lock),  # 然后创建锁定线
            FadeIn(subtitle7),  # 同时显示字幕
            run_time=1.5
        )
        
        # 等待一段时间后淡出字幕
        self.wait(1)
        self.play(FadeOut(subtitle7))
        
        
        # 9. 导弹接近并击中阵风
        
        # 创建字幕
        subtitle8 = create_subtitle("导弹击中阵风战机")
        
        # 显示字幕
        self.play(FadeIn(subtitle8), run_time=0.5)
        
        # 导弹最后冲刺击中阵风
        final_approach = Line(
            pl15.get_center(),
            rafale.get_center(),
            stroke_opacity=0
        )
        
        self.play(
            MoveAlongPath(pl15, final_approach),
            run_time=0.8,
            rate_func=rate_functions.ease_in_quad  # 加速冲刺效果
        )
        
        # 增强爆炸效果
        # 创建多层爆炸效果
        explosion_center = Circle(radius=0.1, fill_color=WHITE, fill_opacity=1, stroke_width=0)
        explosion_inner = Circle(radius=0.05, fill_color=YELLOW, fill_opacity=1, stroke_width=0)
        explosion_middle = Circle(radius=0.03, fill_color=ORANGE, fill_opacity=1, stroke_width=0)
        explosion_outer = Circle(radius=0.02, fill_color=RED, fill_opacity=0.8, stroke_width=0)
        
        explosion_group = VGroup(explosion_center, explosion_inner, explosion_middle, explosion_outer)
        explosion_group.move_to(rafale.get_center())
        
        # 创建火花效果
        sparks = VGroup()
        for _ in range(20):
            angle = np.random.uniform(0, TAU)
            length = np.random.uniform(0.2, 0.5)
            spark = Line(
                rafale.get_center(),
                rafale.get_center() + np.array([np.cos(angle) * length, np.sin(angle) * length, 0]),
                stroke_color=YELLOW,
                stroke_width=2
            )
            sparks.add(spark)
        
        # 创建飞机碎片
        debris = VGroup()
        num_debris = 6  # 碎片数量
        
        for i in range(num_debris):
            # 创建不同形状的碎片
            if i % 3 == 0:
                piece = Triangle(fill_color=RED, fill_opacity=0.8, stroke_width=1).scale(0.1)
            elif i % 3 == 1:
                piece = Square(fill_color=RED, fill_opacity=0.8, stroke_width=1).scale(0.08)
            else:
                piece = Circle(fill_color=RED, fill_opacity=0.8, stroke_width=1).scale(0.05)
            
            # 将碎片放在阵风战机附近但初始不可见
            piece.move_to(rafale.get_center() + np.array([np.random.uniform(-0.2, 0.2), np.random.uniform(-0.2, 0.2), 0]))
            piece.set_opacity(0)  # 初始设置为透明
            debris.add(piece)
        
        # 爆炸动画
        self.add(explosion_group, sparks, debris)  # 立即添加到场景
        
        # 定义地面位置
        ground_y = -3.5  # 屏幕底部附近
        
        # 创建地面线
        ground_line = Line(
            np.array([-7, ground_y, 0]),
            np.array([7, ground_y, 0]),
            color=WHITE,
            stroke_width=2
        )
        self.add(ground_line)  # 直接添加地面线
        
        # 8. 阵风战机被击落
        # 创建字幕对象
        subtitle9 = create_subtitle("阵风战机被击落")
        
        # 创建阵风战机的坠落路径
        fall_path = Line(
            rafale.get_center(),
            np.array([rafale.get_center()[0] - 1, ground_y, 0])  # 向左斜下方坠落到地面
        )
        
        # 创建新的碎片用于坠落动画
        falling_debris = VGroup()
        debris_paths = []
        
        # 创建多个不同的碎片
        for i in range(8):
            # 创建不同形状的碎片
            if i % 3 == 0:
                piece = Triangle(fill_color=RED, fill_opacity=0, stroke_width=1).scale(0.1)
            elif i % 3 == 1:
                piece = Square(fill_color=ORANGE, fill_opacity=0, stroke_width=1).scale(0.08)
            else:
                piece = Circle(fill_color=YELLOW, fill_opacity=0, stroke_width=1).scale(0.05)
            
            # 将碎片放在阵风战机附近
            piece.move_to(rafale.get_center() + np.array([np.random.uniform(-0.3, 0.3), np.random.uniform(-0.3, 0.3), 0]))
            falling_debris.add(piece)
            
            # 创建碎片的坠落路径
            end_point = np.array([
                rafale.get_center()[0] + np.random.uniform(-3, 3),  # x坐标随机分散
                ground_y,  # y坐标均为地面高度
                0
            ])
            
            piece_path = Line(piece.get_center(), end_point)
            debris_paths.append(piece_path)
        
        # 将新碎片添加到场景中
        self.add(falling_debris)
        
        # 爆炸动画
        self.play(
            # 爆炸扩散
            explosion_center.animate.scale(15).set_opacity(0),
            explosion_inner.animate.scale(20).set_opacity(0),
            explosion_middle.animate.scale(25).set_opacity(0),
            explosion_outer.animate.scale(30).set_opacity(0),
            # 火花回带效果
            *[spark.animate.scale(3).set_opacity(0) for spark in sparks],
            # 阵风战机变淡并旋转
            rafale.animate.set_opacity(0.2).rotate(PI/3),
            pl15.animate.set_opacity(0),
            # 碎片显现
            *[piece.animate.set_opacity(1) for piece in falling_debris],
            # 显示字幕
            FadeIn(subtitle9),
            run_time=1
        )
        
        # 立即执行坠落动画，不停顿
        self.play(
            MoveAlongPath(rafale, fall_path),
            *[MoveAlongPath(piece, path) for piece, path in zip(falling_debris, debris_paths)],
            FadeOut(subtitle8),  # 淡出上一个字幕
            run_time=2
        )
        
        # 创建坠落到地面的小爆炸效果
        ground_explosion = Circle(radius=0.3, fill_color=ORANGE, fill_opacity=1, stroke_width=0)
        ground_explosion.move_to(np.array([rafale.get_center()[0] - 1, ground_y, 0]))
        
        # 地面爆炸效果和字幕淡出
        self.play(
            ground_explosion.animate.scale(3).set_opacity(0),
            FadeOut(subtitle9),  # 同时淡出字幕
            run_time=0.8
        )
        