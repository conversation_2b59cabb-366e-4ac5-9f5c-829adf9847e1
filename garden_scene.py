from manim import *

class GardenWithPath(Scene):
    def construct(self):
        # 设置缩放比例，使对象在屏幕中可见
        scale_factor = 0.2
        
        # 创建草地（内部长方形）
        grass = Rectangle(
            width=30 * scale_factor,
            height=15 * scale_factor,
            color=GREEN,
            fill_opacity=0.8
        ).move_to(ORIGIN)
        
        # 添加草地标签
        grass_label = Text("草地: 30 x 15", font="SimSun").scale(0.5).next_to(grass, UP, buff=0.2)
        
        # 首先展示草地
        self.play(Create(grass))
        self.wait(0.5)
        self.play(Write(grass_label))
        self.wait(1)
        
        # 添加草地尺寸标注
        width_arrow = DoubleArrow(start=grass.get_corner(DOWN+LEFT), end=grass.get_corner(DOWN+RIGHT), buff=0.1, color=WHITE)
        width_text = Text("30", font="SimSun").scale(0.5).next_to(width_arrow, DOWN, buff=0.1)
        
        height_arrow = DoubleArrow(start=grass.get_corner(DOWN+RIGHT), end=grass.get_corner(UP+RIGHT), buff=0.1, color=WHITE)
        height_text = Text("15", font="SimSun").scale(0.5).next_to(height_arrow, RIGHT, buff=0.1)
        
        self.play(Create(width_arrow), Write(width_text))
        self.play(Create(height_arrow), Write(height_text))
        self.wait(1)
        
        # 创建小路（通过动画方式添加）
        path_width = 1 * scale_factor
        outer_width = (30 + 2) * scale_factor
        outer_height = (15 + 2) * scale_factor
        
        path_top = Rectangle(width=outer_width, height=path_width, color=LIGHT_BROWN, fill_opacity=0.6)
        path_top.move_to(grass.get_top() + UP * path_width/2)
        
        path_bottom = Rectangle(width=outer_width, height=path_width, color=LIGHT_BROWN, fill_opacity=0.6)
        path_bottom.move_to(grass.get_bottom() + DOWN * path_width/2)
        
        path_left = Rectangle(width=path_width, height=outer_height, color=LIGHT_BROWN, fill_opacity=0.6)
        path_left.move_to(grass.get_left() + LEFT * path_width/2)
        
        path_right = Rectangle(width=path_width, height=outer_height, color=LIGHT_BROWN, fill_opacity=0.6)
        path_right.move_to(grass.get_right() + RIGHT * path_width/2)
        
        paths = VGroup(path_top, path_bottom, path_left, path_right)
        
        # 添加小路标签
        path_label = Text("小路: 宽度 1", font="SimSun").scale(0.5).next_to(grass, DOWN * 2, buff=0.2)
        
        # 动画展示小路 - 分别展示上下部分和左右部分
        # 先展示上下部分
        paths_top_bottom = VGroup(path_top, path_bottom)
        self.play(
            FadeIn(paths_top_bottom, shift=OUT * 0.2, run_time=1.5),
            Write(path_label)
        )
        self.wait(0.5)
        
        # 再展示左右部分
        paths_left_right = VGroup(path_left, path_right)
        self.play(FadeIn(paths_left_right, shift=OUT * 0.2, run_time=1.5))
        self.wait(1)
        
        # 突出显示小路部分
        highlighted_paths = VGroup(
            Rectangle(width=outer_width, height=path_width, color=YELLOW, fill_opacity=0.3).move_to(path_top.get_center()),
            Rectangle(width=outer_width, height=path_width, color=YELLOW, fill_opacity=0.3).move_to(path_bottom.get_center()),
            Rectangle(width=path_width, height=outer_height, color=YELLOW, fill_opacity=0.3).move_to(path_left.get_center()),
            Rectangle(width=path_width, height=outer_height, color=YELLOW, fill_opacity=0.3).move_to(path_right.get_center())
        )
        
        self.play(FadeIn(highlighted_paths))
        self.wait(1)
        
        # 添加小路宽度标注
        path_width_arrow = DoubleArrow(
            start=grass.get_corner(UP+RIGHT), 
            end=grass.get_corner(UP+RIGHT) + RIGHT * path_width, 
            buff=0.05, 
            color=RED
        )
        path_width_text = Text("1", font="SimSun").scale(0.5).next_to(path_width_arrow, UP, buff=0.1)
        
        self.play(Create(path_width_arrow), Write(path_width_text))
        
        # 添加说明文字
        note = Text("(尺寸已按比例缩小)", font="SimSun").scale(0.4).to_corner(DR)
        self.play(Write(note))
        
        self.wait(2)
