import numpy as np
from scipy.io import wavfile
import os

# 确保目录存在
os.makedirs("assets/music", exist_ok=True)

# 生成一个简单的军事风格背景音乐
def generate_military_background_music():
    # 设置参数
    sample_rate = 44100  # 采样率
    duration = 60  # 音乐长度（秒）
    
    # 创建时间数组
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 生成基础鼓点
    drums = np.zeros_like(t)
    beat_interval = 0.5  # 每0.5秒一个鼓点
    for i in range(int(duration / beat_interval)):
        beat_start = int(i * beat_interval * sample_rate)
        beat_end = min(beat_start + 2000, len(drums))
        drums[beat_start:beat_end] = 0.3 * np.exp(-0.01 * np.arange(beat_end - beat_start))
    
    # 生成低音旋律
    bass_freq = 110  # A2音符
    bass = 0.3 * np.sin(2 * np.pi * bass_freq * t)
    
    # 生成高音旋律（军事风格的号角声）
    melody = np.zeros_like(t)
    melody_notes = [440, 523.25, 587.33, 659.25]  # A4, C5, D5, E5
    note_duration = 1.0  # 每个音符持续1秒
    
    for i in range(int(duration / note_duration)):
        note_freq = melody_notes[i % len(melody_notes)]
        note_start = int(i * note_duration * sample_rate)
        note_end = min(note_start + int(note_duration * sample_rate), len(melody))
        note_t = np.arange(note_end - note_start) / sample_rate
        melody[note_start:note_end] = 0.2 * np.sin(2 * np.pi * note_freq * note_t)
    
    # 混合所有轨道
    mixed = drums + bass + melody
    
    # 标准化音量
    mixed = mixed / np.max(np.abs(mixed)) * 0.9
    
    # 转换为16位整数
    audio_data = (mixed * 32767).astype(np.int16)
    
    # 保存为WAV文件
    wavfile.write("assets/music/background_music.wav", sample_rate, audio_data)
    
    print("背景音乐已生成：assets/music/background_music.wav")

if __name__ == "__main__":
    generate_military_background_music()
