from manim import *

class BoilingWater(Scene):
    def construct(self):
        # 创建锅
        pot_bottom = Arc(angle=PI, start_angle=0, radius=1.5)
        pot_left = Line(pot_bottom.get_start(), pot_bottom.get_start() + UP * 0.8)
        pot_right = Line(pot_bottom.get_end(), pot_bottom.get_end() + UP * 0.8)
        pot_top_left = Arc(angle=PI/6, start_angle=PI-PI/6, radius=1.5).shift(UP * 0.8)
        pot_top_right = Arc(angle=PI/6, start_angle=0, radius=1.5).shift(UP * 0.8)
        pot_handle_left = Line(pot_top_left.get_start(), pot_top_left.get_start() + LEFT * 0.5 + UP * 0.2)
        pot_handle_right = Line(pot_top_right.get_end(), pot_top_right.get_end() + RIGHT * 0.5 + UP * 0.2)
        
        pot = VGroup(pot_bottom, pot_left, pot_right, pot_top_left, pot_top_right, 
                     pot_handle_left, pot_handle_right)
        pot.set_stroke(WHITE, 3)
        pot.move_to(ORIGIN)
        
        # 创建水
        water = Polygon(
            pot_bottom.get_start() + UP * 0.2 + RIGHT * 0.2,
            pot_bottom.get_end() + UP * 0.2 + LEFT * 0.2,
            pot_bottom.get_end() + UP * 0.5 + LEFT * 0.2,
            pot_bottom.get_start() + UP * 0.5 + RIGHT * 0.2,
            fill_opacity=0.8,
            fill_color=BLUE,
            stroke_width=0
        )
        
        # 创建火焰
        fire_base = Triangle(fill_color=RED, fill_opacity=1, stroke_width=0)
        fire_base.scale(0.7)
        fire_base.next_to(pot_bottom, DOWN, buff=0.1)
        
        fire_middle = Triangle(fill_color=ORANGE, fill_opacity=1, stroke_width=0)
        fire_middle.scale(0.5)
        fire_middle.next_to(pot_bottom, DOWN, buff=0.2)
        
        fire_top = Triangle(fill_color=YELLOW, fill_opacity=1, stroke_width=0)
        fire_top.scale(0.3)
        fire_top.next_to(pot_bottom, DOWN, buff=0.3)
        
        fire = VGroup(fire_base, fire_middle, fire_top)
        
        # 创建蒸汽（3条曲线）
        steam1 = CubicBezier(
            start=pot_top_left.get_center() + UP * 0.1,
            control_1=pot_top_left.get_center() + UP * 0.5 + LEFT * 0.2,
            control_2=pot_top_left.get_center() + UP * 1.0 + LEFT * 0.1,
            end=pot_top_left.get_center() + UP * 1.5,
        )
        
        steam2 = CubicBezier(
            start=pot.get_top() + UP * 0.1,
            control_1=pot.get_top() + UP * 0.5,
            control_2=pot.get_top() + UP * 1.0,
            end=pot.get_top() + UP * 1.5,
        )
        
        steam3 = CubicBezier(
            start=pot_top_right.get_center() + UP * 0.1,
            control_1=pot_top_right.get_center() + UP * 0.5 + RIGHT * 0.2,
            control_2=pot_top_right.get_center() + UP * 1.0 + RIGHT * 0.1,
            end=pot_top_right.get_center() + UP * 1.5,
        )
        
        steam = VGroup(steam1, steam2, steam3)
        steam.set_stroke(WHITE, opacity=0.6, width=2)
        
        # 添加元素到场景
        self.play(Create(pot))
        self.play(FadeIn(water))
        self.play(FadeIn(fire))
        
        # 火焰动画效果
        self.play(
            fire_base.animate.scale(1.1).shift(DOWN * 0.05),
            fire_middle.animate.scale(1.15).shift(UP * 0.05),
            fire_top.animate.scale(1.2).shift(UP * 0.1),
            rate_func=there_and_back,
            run_time=1
        )
        
        # 水开始沸腾的动画
        bubbles = VGroup()
        for i in range(5):
            bubble = Circle(radius=0.05, fill_color=WHITE, fill_opacity=0.7, stroke_width=0)
            x_pos = np.random.uniform(-1.0, 1.0)
            bubble.move_to(water.get_center() + RIGHT * x_pos)
            bubbles.add(bubble)
        
        # 气泡上升动画
        self.play(FadeIn(bubbles))
        self.play(
            *[bubble.animate.shift(UP * np.random.uniform(0.2, 0.4)) for bubble in bubbles],
            rate_func=linear,
            run_time=1.5
        )
        self.play(FadeOut(bubbles))
        
        # 显示蒸汽
        self.play(Create(steam), run_time=1.5)
        
        # 蒸汽上升动画
        for _ in range(3):
            self.play(
                *[steam_line.animate.shift(UP * 0.2).set_opacity(
                    np.random.uniform(0.4, 0.7)) for steam_line in steam],
                rate_func=there_and_back,
                run_time=1
            )
        
        # 火焰持续燃烧的动画
        for _ in range(2):
            self.play(
                fire_base.animate.scale(1.1).shift(DOWN * 0.05),
                fire_middle.animate.scale(1.15).shift(UP * 0.05),
                fire_top.animate.scale(1.2).shift(UP * 0.1),
                rate_func=there_and_back,
                run_time=0.8
            )
            
            # 同时水也在沸腾
            new_bubbles = VGroup()
            for i in range(5):
                bubble = Circle(radius=0.05, fill_color=WHITE, fill_opacity=0.7, stroke_width=0)
                x_pos = np.random.uniform(-1.0, 1.0)
                bubble.move_to(water.get_center() + RIGHT * x_pos)
                new_bubbles.add(bubble)
                
            self.play(
                FadeIn(new_bubbles),
                *[steam_line.animate.shift(UP * 0.3).set_opacity(
                    np.random.uniform(0.4, 0.7)) for steam_line in steam],
                run_time=0.8
            )
            self.play(
                *[bubble.animate.shift(UP * np.random.uniform(0.2, 0.4)) for bubble in new_bubbles],
                rate_func=linear,
                run_time=0.8
            )
            self.play(FadeOut(new_bubbles))
        
        # 最后的场景
        self.wait(1)
