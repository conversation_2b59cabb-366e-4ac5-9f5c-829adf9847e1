from manim import *

class SquareToCircle(Scene):
    def construct(self):
        # 创建一个正方形
        square = Square(color=BLUE, fill_opacity=0.5)
        
        # 显示正方形
        self.play(Create(square))
        
        # 等待一秒
        self.wait(1)
        
        # 将正方形变形为圆形
        circle = Circle(color=RED, fill_opacity=0.5)
        self.play(Transform(square, circle))
        
        # 等待一秒
        self.wait(1)
        
        # 移动到屏幕右侧
        self.play(square.animate.shift(RIGHT * 2))
        
        # 等待一秒
        self.wait(1)

class MovingFrameBox(Scene):
    def construct(self):
        # 创建文本
        text = Text("这是一个Manim动画示例", font="SimSun")
        self.play(Write(text))
        
        # 创建一个框
        frame_box = SurroundingRectangle(text, buff=0.1, color=YELLOW)
        self.play(Create(frame_box))
        
        # 等待一秒
        self.wait(1)
        
        # 创建另一个文本
        text2 = Text("Manim很强大！", font="SimSun").next_to(text, DOWN)
        self.play(Write(text2))
        
        # 移动框到新文本
        frame_box2 = SurroundingRectangle(text2, buff=0.1, color=GREEN)
        self.play(Transform(frame_box, frame_box2))
        
        # 等待两秒
        self.wait(2)
