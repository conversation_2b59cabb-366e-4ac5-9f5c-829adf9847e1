{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f93f4111-416a-44b0-8777-5029eca3dc0d", "metadata": {}, "outputs": [], "source": ["from manim import *\n", "import numpy as np\n"]}, {"cell_type": "code", "execution_count": 2, "id": "4414dbc8", "metadata": {}, "outputs": [], "source": ["def create_normal_tongue_parts(self):\n", "    # 创建舌质（舌头的肉质部分）\n", "    tongue_body = Ellipse(\n", "        width=1.6, height=1.0,\n", "        fill_color=rgb_to_color([0.9, 0.5, 0.5]),\n", "        fill_opacity=0.95,\n", "        stroke_color=rgb_to_color([0.85, 0.45, 0.45]),\n", "        stroke_width=1\n", "    )\n", "    \n", "    # 创建舌苔（舌头表面的薄薄一层白苔）\n", "    tongue_coating = Ellipse(\n", "        width=1.5, height=0.9,\n", "        fill_color=WHITE,\n", "        fill_opacity=0.3,\n", "        stroke_width=0\n", "    )\n", "    \n", "    return tongue_body, tongue_coating"]}, {"cell_type": "code", "execution_count": 3, "id": "d082a406", "metadata": {}, "outputs": [], "source": ["class CompleteTongueScene(Scene):\n", "    \"\"\"完整的舌诊教学场景，包含部位介绍、观察维度、正常舌象和异常舌象\"\"\"\n", "    \n", "    def construct(self):\n", "        # 设置背景为白色\n", "        self.camera.background_color = \"#FFFFFF\"\n", "\n", "        # 第一部分：舌部位解析与观察维度介绍\n", "        self.show_tongue_analysis()"]}, {"cell_type": "code", "execution_count": 4, "id": "9ae75b39", "metadata": {}, "outputs": [], "source": ["def show_tongue_analysis(self):\n", "    \"\"\"第一部分：舌部位解析与观察维度介绍\"\"\"\n", "    # 创建主标题\n", "    main_title = Text(\"中医舌诊\", font=\"SimHei\", color=BLACK).scale(1.2)\n", "    main_title.to_edge(UP, buff=0.3)\n", "    self.play(Write(main_title))\n", "    self.wait(1)\n", "\n", "    # 创建第一部分标题\n", "    part1_title = Text(\"第一部分：舌部位解析与观察维度\", font=\"SimHei\", color=BLUE).scale(0.8)\n", "    part1_title.next_to(main_title, DOWN, buff=0.5)\n", "    self.play(Write(part1_title))\n", "    self.wait(1)\n", "\n", "    # 创建舌头的各个部分 - 调整位置使其更居中\n", "    tongue_body, tongue_coating = self.create_normal_tongue_parts()\n", "    tongue_group = VGroup(tongue_body, tongue_coating)\n", "    tongue_group.scale(0.8).move_to(ORIGIN)\n", "\n", "    # 首先同时显示舌质和舌苔\n", "    self.play(FadeIn(tongue_group))\n", "    self.wait(1)\n", "    \n", "    # 舌头整体标签\n", "    tongue_label = Text(\"舌头整体\", font=\"SimHei\", color=BLACK).scale(0.7)\n", "    tongue_label.next_to(tongue_group, DOWN, buff=0.8)\n", "    self.play(Write(tongue_label))\n", "    self.wait(1)"]}, {"cell_type": "code", "execution_count": 5, "id": "221ed435", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'tongue_body' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 动画左右分离舌质和舌苔\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# 创建舌质和舌苔的副本用于分离展示\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m tongue_body_left = \u001b[43mtongue_body\u001b[49m.copy()\n\u001b[32m      4\u001b[39m tongue_coating_right = tongue_coating.copy()\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# 设置目标位置\u001b[39;00m\n", "\u001b[31mNameError\u001b[39m: name 'tongue_body' is not defined"]}], "source": ["    # 动画左右分离舌质和舌苔\n", "    # 创建舌质和舌苔的副本用于分离展示\n", "    tongue_body_left = tongue_body.copy()\n", "    tongue_coating_right = tongue_coating.copy()\n", "    \n", "    # 设置目标位置\n", "    tongue_body_left.generate_target()\n", "    tongue_coating_right.generate_target()\n", "    tongue_body_left.target.move_to(LEFT * 3)\n", "    tongue_coating_right.target.move_to(RIGHT * 1)\n", "    \n", "    # 执行分离动画\n", "    self.play(\n", "        FadeOut(tongue_group),\n", "        FadeOut(tongue_label),\n", "        MoveToTarget(tongue_body_left),\n", "        MoveToTarget(tongue_coating_right)\n", "    )\n", "    \n", "    # 添加舌质标签和说明\n", "    tongue_body_label = Text(\"舌质\", font=\"SimHei\", color=BLACK).scale(0.7)\n", "    tongue_body_label.next_to(tongue_body_left, DOWN, buff=0.5)\n", "    \n", "    # 舌质说明文本\n", "    tongue_body_text = Text(\n", "        \"舌头的肉质部分\",\n", "        font=\"SimHei\",\n", "        color=BLACK,\n", "        line_spacing=1.2\n", "    ).scale(0.55)\n", "    tongue_body_text.next_to(tongue_body_label, DOWN, buff=0.5)\n", "    \n", "    # 添加舌苔标签和说明\n", "    tongue_coating_label = Text(\"舌苔\", font=\"SimHei\", color=BLACK).scale(0.7)\n", "    tongue_coating_label.next_to(tongue_body_label, RIGHT, buff=3.2)\n", "    \n", "    # 舌苔说明文本\n", "    tongue_coating_text = Text(\n", "        \"舌头表面的薄薄一层白苔\",\n", "        font=\"SimHei\",\n", "        color=BLACK,\n", "        line_spacing=1.2\n", "    ).scale(0.55)\n", "    tongue_coating_text.next_to(tongue_coating_label, DOWN, buff=0.5)"]}, {"cell_type": "code", "execution_count": null, "id": "59c5dc8c", "metadata": {}, "outputs": [], "source": ["    # 创建舌苔的副本用于分离展示\n", "    tongue_coating_right = self.create_normal_tongue_parts()[1]\n", "    \n", "    # 中间舌质变形回左侧的舌质\n", "    tongue_body_left = self.create_normal_tongue_parts()[0]\n", "    tongue_body_left.scale(0.8).move_to(LEFT * 3)\n", "    \n", "    # 将中间的舌质变形回左侧\n", "    self.play(\n", "        Transform(original_tongue, tongue_body_left)\n", "    )\n", "    self.wait(0.3)\n", "    \n", "    # 设置舌苔位置\n", "    tongue_coating_right.scale(0.8).move_to(RIGHT * 1)\n", "    \n", "    # 添加标签\n", "    tongue_body_label = Text(\"舌质\", font=\"SimHei\", color=BLACK).scale(0.7)\n", "    tongue_body_label.next_to(tongue_body_left, DOWN, buff=0.5)\n", "    \n", "    tongue_coating_label = Text(\"舌苔\", font=\"SimHei\", color=BLACK).scale(0.7)\n", "    tongue_coating_label.next_to(tongue_coating_right, DOWN, buff=0.5)\n", "    \n", "    # 显示舌苔和标签\n", "    self.play(\n", "        FadeIn(tongue_coating_right),\n", "        FadeIn(tongue_body_label),\n", "        FadeIn(tongue_coating_label)\n", "    )\n", "    self.wait(0.5)"]}, {"cell_type": "code", "execution_count": null, "id": "862c2b0b", "metadata": {}, "outputs": [], "source": ["# 使用魔术命令渲染场景\n", "%%manim -qm CompleteTongueScene"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}