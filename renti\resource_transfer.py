from manim import *

class ResourceTransfer(Scene):
    def construct(self):
        # 创建两个同心圆
        inner_circle = Circle(radius=1.5, color=BLUE, fill_opacity=0.5)
        outer_circle = Circle(radius=3, color=GREEN, fill_opacity=0.3)
        
        # 添加标签
        inner_label = Text("脏腑", font="SimSun").scale(0.6).move_to(inner_circle)
        outer_label = Text("肌表", font="SimSun").scale(0.6)
        outer_label.move_to(outer_circle.point_at_angle(PI/4)).shift(RIGHT * 0.5 + UP * 0.5)
        
        # 创建资源点
        resources = VGroup(*[
            Dot(color=RED, radius=0.08).move_to(
                inner_circle.point_at_angle(angle)
            )
            for angle in np.linspace(0, 2*PI, 8, endpoint=False)
        ])
        
        # 创建传输路径
        paths = VGroup()
        for i, dot in enumerate(resources):
            angle = i * 2 * PI / 8
            end_point = outer_circle.point_at_angle(angle)
            path = Line(dot.get_center(), end_point, color=YELLOW, stroke_width=2)
            paths.add(path)
        
        # 显示圆圈和标签
        self.play(Create(outer_circle), Write(outer_label))
        self.play(Create(inner_circle), Write(inner_label))
        self.wait(0.5)
        
        # 显示资源点
        self.play(FadeIn(resources, lag_ratio=0.1))
        self.wait(0.5)
        
        # 创建资源传输动画
        self.play(Create(paths, lag_ratio=0.1))
        
        # 资源传输动画
        moving_dots = VGroup()
        for i, (dot, path) in enumerate(zip(resources, paths)):
            moving_dot = Dot(color=RED, radius=0.08).move_to(dot.get_center())
            moving_dots.add(moving_dot)
        
        self.play(FadeIn(moving_dots))
        
        # 创建资源传输动画
        for i in range(3):  # 重复传输3次
            self.play(
                *[
                    MoveAlongPath(dot, path, rate_func=rate_functions.ease_in_out_sine, run_time=2)
                    for dot, path in zip(moving_dots, paths)
                ],
                lag_ratio=0.05
            )
            self.wait(0.3)
            
            # 资源回到内圈
            self.play(
                *[
                    dot.animate.move_to(resources[i].get_center())
                    for i, dot in enumerate(moving_dots)
                ],
                run_time=1
            )
            self.wait(0.3)
        
        # 添加说明文字
        explanation = Text("资源从脏腑向形体传输", font="SimSun").scale(0.7).to_edge(DOWN)
        self.play(Write(explanation))
        
        # 突出显示传输路径
        highlighted_paths = VGroup(*[
            Line(
                resources[i].get_center(), 
                outer_circle.point_at_angle(i * 2 * PI / 8), 
                color=WHITE, 
                stroke_width=3
            ).add_tip(tip_length=0.2)
            for i in range(8)
        ])
        
        self.play(
            FadeOut(paths),
            FadeIn(highlighted_paths)
        )
        
        self.wait(2)
        
        # 创建物质和能量的符号
        matter_symbols = VGroup()
        energy_symbols = VGroup()
        
        # 物质用方形表示，能量用星形表示
        for i in range(8):
            if i % 2 == 0:  # 偶数位置放物质符号
                matter = Square(side_length=0.2, color=BLUE).move_to(
                    outer_circle.point_at_angle(i * 2 * PI / 8)
                ).set_fill(BLUE, opacity=0.8)
                matter_symbols.add(matter)
            else:  # 奇数位置放能量符号
                # 创建一个简单的星形
                energy = Star(n=5, outer_radius=0.15, color=YELLOW).move_to(
                    outer_circle.point_at_angle(i * 2 * PI / 8)
                ).set_fill(YELLOW, opacity=0.8)
                energy_symbols.add(energy)
        
        # 添加物质和能量的标签
        matter_label = Text("物质", font="SimSun").scale(0.5).next_to(matter_symbols[0], UP+RIGHT, buff=0.2)
        energy_label = Text("能量", font="SimSun").scale(0.5).next_to(energy_symbols[0], UP+LEFT, buff=0.2)
        
        # 资源点转变为物质和能量符号的动画
        self.play(
            FadeOut(highlighted_paths),
            *[Transform(resources[i], matter_symbols[i//2]) for i in range(0, 8, 2)],
            *[Transform(resources[i], energy_symbols[i//2]) for i in range(1, 8, 2)]
        )
        
        # 显示标签
        self.play(
            Write(matter_label),
            Write(energy_label)
        )
        
        # 更新说明文字
        new_explanation = Text("脏腑向形体传输物质与能量", font="SimSun").scale(0.7).to_edge(DOWN)
        self.play(Transform(explanation, new_explanation))
        
        # 添加脏腑与形体的关系说明
        relation_text = Text("脏腑生成并输送物质与能量，形体接收并利用", font="SimSun").scale(0.6).to_edge(UP)
        self.play(Write(relation_text))
        
        self.wait(2)
        
        # 转场动画：选择一个物质和一个能量符号进行放大
        selected_matter = resources[0].copy()
        selected_energy = resources[1].copy()
        
        # 淡出所有其他元素，只保留选中的符号
        self.play(
            FadeOut(inner_circle),
            FadeOut(outer_circle),
            FadeOut(inner_label),
            FadeOut(outer_label),
            FadeOut(explanation),
            FadeOut(relation_text),
            FadeOut(matter_label),
            FadeOut(energy_label),
            *[FadeOut(resources[i]) for i in range(2, 8)],
            selected_matter.animate.move_to(LEFT * 3),
            selected_energy.animate.move_to(RIGHT * 3)
        )
        
        # 放大选中的符号并添加详细说明
        self.play(
            selected_matter.animate.scale(2),
            selected_energy.animate.scale(2)
        )
        
        # 添加物质的详细说明
        matter_detail = VGroup(
            Text("物质", font="SimSun").scale(0.8),
            Text("• 津液", font="SimSun").scale(0.6),
            Text("• 血液", font="SimSun").scale(0.6),
            Text("• 营养物质", font="SimSun").scale(0.6)
        ).arrange(DOWN, aligned_edge=LEFT).next_to(selected_matter, DOWN, buff=0.5)
        
        # 添加能量的详细说明
        energy_detail = VGroup(
            Text("能量", font="SimSun").scale(0.8),
            Text("• 气", font="SimSun").scale(0.6),
            Text("• 热能", font="SimSun").scale(0.6),
            Text("• 生物电能", font="SimSun").scale(0.6)
        ).arrange(DOWN, aligned_edge=LEFT).next_to(selected_energy, DOWN, buff=0.5)
        
        # 显示详细说明
        self.play(
            Write(matter_detail),
            Write(energy_detail)
        )
        
        # 添加总结文字
        summary = Text("中医理论中，脏腑产生并输送物质与能量，维持人体正常运转", font="SimSun").scale(0.7).to_edge(UP)
        self.play(Write(summary))
        
        self.wait(2)
