from manim import *
import numpy as np

class SimpleOrganResourceFlow(Scene):
    def construct(self):
        # 创建简易人体轮廓
        # 头部
        head = Circle(radius=0.5, color=WHITE).set_fill(opacity=0)
        head.move_to(UP * 2.5)
        
        # 躯干
        body = Rectangle(height=3, width=1.5, color=WHITE).set_fill(opacity=0)
        body.move_to(UP * 0.5)
        
        # 四肢
        left_arm = Line(body.get_corner(UL) + LEFT * 0.2, LEFT * 1.5 + UP * 0.5, color=WHITE)
        right_arm = Line(body.get_corner(UR) + RIGHT * 0.2, RIGHT * 1.5 + UP * 0.5, color=WHITE)
        
        left_leg = Line(body.get_corner(DL), LEFT * 0.5 + DOWN * 2, color=WHITE)
        right_leg = Line(body.get_corner(DR), RIGHT * 0.5 + DOWN * 2, color=WHITE)
        
        # 人体轮廓组合
        human_outline = VGroup(head, body, left_arm, right_arm, left_leg, right_leg)
        
        # 创建五脏位置（在躯干内部合理位置）
        positions = {
            "心脏": UP * 1.2 + LEFT * 0.3,  # 左胸位置
            "肺": UP * 1.2 + RIGHT * 0.3,  # 右胸位置
            "肝脏": UP * 0.2 + RIGHT * 0.3,  # 右上腹位置
            "脾脏": UP * 0.2 + LEFT * 0.3,   # 左上腹位置
            "肾脏": DOWN * 0.5             # 下腹位置
        }
        
        # 创建五脏（简单的圆形）
        organs = {}
        labels = {}
        
        organ_colors = {
            "心脏": RED,
            "肺": BLUE_C,
            "肝脏": "#8B4513",  # 棕色
            "脾脏": PURPLE,
            "肾脏": "#A52A2A"   # 深红色
        }
        
        for name, pos in positions.items():
            organ = Circle(radius=0.25, color=organ_colors[name]).set_fill(organ_colors[name], opacity=0.7)
            organ.move_to(pos)
            organs[name] = organ
            
            label = Text(name, font_size=16, color=WHITE)
            label.next_to(organ, DOWN, buff=0.05)
            labels[name] = label
        
        # 创建全身循环示意（外圈）
        body_circle = Circle(radius=3.5, color=WHITE).set_stroke(opacity=0.3)
        body_label = Text("全身", font_size=20).move_to(UP * 3)
        
        # 添加人体轮廓到场景
        self.play(Create(human_outline), run_time=1.5)
        
        # 添加全身循环圈
        self.play(Create(body_circle), Write(body_label), run_time=1)
        
        # 添加器官和标签
        self.play(
            *[FadeIn(organ) for organ in organs.values()],
            *[Write(label) for label in labels.values()],
            run_time=1.5
        )
        
        # 创建通道（简单的直线）
        channels = {}
        for name, organ in organs.items():
            organ_channels = []
            # 每个器官2条通道
            for i in range(2):
                # 计算通道起点和终点
                start = organ.get_center()
                # 计算通道终点在全身圆上的位置
                angle = np.random.uniform(0, 2*PI)
                end = body_circle.point_at_angle(angle)
                
                # 创建直线通道
                channel = Line(start, end, color=WHITE, stroke_width=1.5, stroke_opacity=0.6)
                organ_channels.append(channel)
            channels[name] = organ_channels
        
        # 添加通道
        all_channels = [channel for organ_channels in channels.values() for channel in organ_channels]
        self.play(*[Create(channel) for channel in all_channels], run_time=1.5)
        
        # 定义资源类型和颜色
        resources = {
            "心脏": {"氧气": RED, "血液": PINK},
            "肺": {"氧气": BLUE_A, "二氧化碳": BLUE_E},
            "肝脏": {"糖原": YELLOW, "蛋白质": GREEN},
            "脾脏": {"免疫细胞": PURPLE_A, "血液过滤": PURPLE_E},
            "肾脏": {"废物": GRAY, "水分": TEAL}
        }
        
        # 资源粒子动画（单个器官）
        for organ_name, organ_resources in resources.items():
            organ_channels = channels[organ_name]
            
            # 显示资源名称
            resource_labels = []
            for i, (resource_name, color) in enumerate(organ_resources.items()):
                resource_label = Text(resource_name, font_size=14, color=color)
                resource_label.next_to(organs[organ_name], UP + (LEFT if i == 0 else RIGHT), buff=0.1)
                resource_labels.append(resource_label)
                
            self.play(*[Write(label) for label in resource_labels], run_time=0.5)
            
            # 为每种资源创建粒子并沿通道移动
            for i, (resource_name, color) in enumerate(organ_resources.items()):
                if i < len(organ_channels):
                    channel = organ_channels[i]
                    dot = Dot(color=color, radius=0.05)
                    dot.move_to(channel.get_start())
                    
                    # 添加粒子
                    self.add(dot)
                    
                    # 粒子沿通道移动
                    self.play(
                        dot.animate.move_to(channel.get_end()),
                        run_time=1,
                        rate_func=smooth
                    )
                    
                    # 粒子到达终点后闪烁并消失
                    self.play(dot.animate.scale(1.5), run_time=0.2)
                    self.play(FadeOut(dot), run_time=0.2)
            
            # 移除资源标签
            self.play(*[FadeOut(label) for label in resource_labels], run_time=0.3)
        
        # 所有器官同时发送资源
        all_dots = []
        
        # 为每个器官的每个通道创建一个资源粒子
        for organ_name, organ_resources in resources.items():
            organ_channels = channels[organ_name]
            
            for i, (resource_name, color) in enumerate(organ_resources.items()):
                if i < len(organ_channels):
                    channel = organ_channels[i]
                    dot = Dot(color=color, radius=0.05)
                    dot.move_to(channel.get_start())
                    all_dots.append((dot, channel))
        
        # 添加所有粒子
        self.play(*[FadeIn(dot) for dot, _ in all_dots], run_time=0.5)
        
        # 所有粒子同时沿各自通道移动
        self.play(
            *[dot.animate.move_to(channel.get_end()) for dot, channel in all_dots],
            run_time=1.5,
            rate_func=smooth
        )
        
        # 所有粒子到达终点后闪烁并消失
        self.play(
            *[dot.animate.scale(1.5) for dot, _ in all_dots],
            run_time=0.3
        )
        self.play(
            *[FadeOut(dot) for dot, _ in all_dots],
            run_time=0.3
        )
        
        # 显示完成信息
        completion_text = Text("人体五脏资源传输演示完成", font_size=32)
        self.play(Write(completion_text), run_time=1)
        self.wait(1)
        self.play(FadeOut(completion_text), run_time=0.5)
