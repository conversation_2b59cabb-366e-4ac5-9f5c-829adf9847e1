from manim import *
from manim.scene.section import Section

class SectionDemo(Scene):
    def construct(self):
        # 1. 基本章节用法
        self.next_section("场景初始化", section_type="normal",skip_animations=True)
        circle = Circle(color=BLUE)
        self.play(Create(circle))
        
        # 2. 嵌套章节
        self.next_section("主动画流程", section_type="normal")
        
        # 2.1 变形效果示例
        self.next_section("变形效果", section_type="example")
        self.play(Transform(circle, Square()))
            
        # 3. 条件章节（仅在DEBUG时执行）
        if hasattr(self, 'debug_mode') and self.debug_mode:
            self.next_section("调试动画", section_type="normal")
            debug_text = Text("DEBUG模式", color=RED)
            self.play(Write(debug_text))
            self.play(FadeOut(debug_text))
            
        # 4. 性能分析章节
        self.next_section("复杂计算", section_type="normal")
        # 减少循环次数以加快演示
        for i in range(10):
            self.play(Rotate(circle, angle=0.1*PI))
            
        # 5. 自定义样式章节
        self.next_section("警告提示", section_type="warning")
        alert = Triangle(color=RED).scale(0.5)
        self.play(Flash(alert))
        
        # 6. 结束场景
        self.next_section("结束场景", section_type="normal")
        # 添加一个文本模拟章节事件触发
        self.add(Text("章节事件触发", color=GREEN))
        self.play(FadeOut(circle))