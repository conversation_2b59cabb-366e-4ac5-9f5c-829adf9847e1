from manim import *
import numpy as np

# 控制哪些部分需要渲染的标志变量
# 将需要渲染的部分设置为True，不需要渲染的部分设置为False
RENDER_INTRO = True           # 开场和背景介绍
RENDER_THUNDERCLOUD = True    # 雷雨云的形成
RENDER_INITIAL = True         # 冰雹形成的初始阶段
RENDER_GROWTH = True          # 冰雹的生长过程
RENDER_FALLING = True         # 冰雹的最终形成和降落
RENDER_SUMMARY = True         # 科学解释和总结

class HailstoneFormation(Scene):
    def construct(self):
        # 添加作者水印
        author_watermark = Text("萤知月", font="SimSun", color=WHITE, opacity=0.5).scale(0.5)
        author_watermark.to_corner(DR, buff=0.3)
        self.add_foreground_mobject(author_watermark)
        
        # 创建标题对象，在整个动画中使用
        self.main_title = Text("冰雹的形成原理", font="SimSun", color=BLUE).scale(0.8)
        self.main_title.to_edge(UP)
        
        # 初始化全局变量，保证即使跳过某些场景也能正常运行
        self.initialize_global_variables()
        
        # 根据标志变量决定是否执行各个场景
        if RENDER_INTRO:
            self.intro_scene()
        
        if RENDER_THUNDERCLOUD:
            self.thundercloud_formation()
        
        if RENDER_INITIAL:
            self.initial_hailstone_formation()
        
        if RENDER_GROWTH:
            self.hailstone_growth()
            self.hailstone_size_factors()  # 添加冰雹大小因素场景
        
        if RENDER_FALLING:
            self.hailstone_falling()
        
        if RENDER_SUMMARY:
            self.summary()
            
    def initialize_global_variables(self):
        """初始化全局变量，确保即使跳过某些场景也能正常运行"""
        # 初始化标题对象
        self.title = self.main_title
        
        # 初始化雷雨云场景对象
        if not RENDER_THUNDERCLOUD:
            # 创建默认的地面和天空
            ground = Rectangle(height=1, width=14, fill_color=GREEN_E, fill_opacity=1)
            ground.move_to(DOWN * 3)
            sky = Rectangle(height=7, width=14, fill_color=BLUE_E, fill_opacity=0.3)
            sky.move_to(UP * 0.5)
            
            # 创建默认的云
            cloud_base = Polygon(
                np.array([-5, 0, 0]),
                np.array([5, 0, 0]),
                np.array([4, 1, 0]),
                np.array([-4, 1, 0]),
                fill_color=LIGHT_GREY, fill_opacity=0.8, stroke_width=0
            )
            cloud_top = Polygon(
                np.array([-4, 1, 0]),
                np.array([4, 1, 0]),
                np.array([2, 3, 0]),
                np.array([-2, 3, 0]),
                fill_color=DARK_GREY, fill_opacity=0.9, stroke_width=0
            )
            cloud = VGroup(cloud_base, cloud_top)
            
            # 创建默认的温度线
            # 调整虚线起点，不要太靠近屏幕左边缘
            zero_degree_line = DashedLine(
                np.array([-6, 1, 0]),  # 从-6改为-5，向右移动起点
                np.array([6, 1, 0]),
                color=BLUE
            )
            zero_degree_text = Text("0°C", font="SimSun", color=BLUE).scale(0.5)
            # 将文本放在虚线的左端上方，但向右偏移一些
            # 先放在线的起点上方
            zero_degree_text.next_to(zero_degree_line.get_start(), UP, buff=0.3)
            # 然后向右偏移
            zero_degree_text.shift(RIGHT * 1.5)
            
            # 调整虚线起点，不要太靠近屏幕左边缘
            minus_forty_line = DashedLine(
                np.array([-6, 2.5, 0]),  # 从-6改为-5，向右移动起点
                np.array([6, 2.5, 0]),
                color=BLUE_E
            )
            minus_forty_text = Text("-40°C", font="SimSun", color=BLUE_E).scale(0.5)
            # 将文本放在虚线的左端上方，但向右偏移一些
            # 先放在线的起点上方
            minus_forty_text.next_to(minus_forty_line.get_start(), UP, buff=0.3)
            # 然后向右偏移
            minus_forty_text.shift(RIGHT * 1.5)
            
            # 存储到全局变量
            self.cloud = cloud
            self.zero_degree_line = zero_degree_line
            self.zero_degree_text = zero_degree_text
            self.minus_forty_line = minus_forty_line
            self.minus_forty_text = minus_forty_text
            self.ground = ground
            self.sky = sky
            self.sun = Circle(radius=0.5, fill_color=YELLOW, fill_opacity=1).move_to(UP * 2 + RIGHT * 5)
        
        # 初始化冰晶对象
        if not RENDER_INITIAL and (RENDER_GROWTH or RENDER_FALLING):
            ice_crystals = VGroup()
            crystal = Star(n=6, outer_radius=0.08, inner_radius=0.05, 
                          fill_color=WHITE, fill_opacity=1, stroke_width=0)
            crystal.move_to(ORIGIN)
            ice_crystals.add(crystal)
            self.ice_crystals = ice_crystals
        
        # 初始化冰雹对象
        if not RENDER_GROWTH and RENDER_FALLING:
            even_bigger_hailstone = Circle(radius=0.5, fill_color=WHITE, fill_opacity=1, stroke_color=BLUE_E, stroke_width=1)
            even_bigger_hailstone.move_to(ORIGIN)
            self.final_hailstone = even_bigger_hailstone
    
    def intro_scene(self):
        # 大标题
        title = Text("冰雹的形成原理", font="SimSun", color=BLUE).scale(1.5)
        self.play(Write(title))
        self.wait(1)
        
        # 将大标题缩小并移动到顶部，与我们预先创建的标题对象相匹配
        self.play(Transform(title, self.main_title))
        
        # 简短介绍冰雹现象及其危害
        intro_text = Text(
            "冰雹是一种由冰块组成的降水，通常在强烈的雷暴天气中形成。\n"
            "它可能造成农作物损失、车辆损坏，甚至威胁人身安全。",
            font="SimSun", color=WHITE
        ).scale(0.6)
        
        self.play(FadeIn(intro_text))
        self.wait(2)
        self.play(FadeOut(intro_text))
        
        # 保留标题对象供后续使用
        self.title = title
    
    def thundercloud_formation(self):
        # 更新标题
        subtitle = Text("雷雨云的形成", font="SimSun", color=BLUE).scale(0.8)
        subtitle.to_edge(UP)
        self.play(FadeOut(self.title), FadeIn(subtitle))
        
        # 地面和天空
        ground = Rectangle(height=1, width=14, fill_color=GREEN_E, fill_opacity=1)
        ground.move_to(DOWN * 3)
        sky = Rectangle(height=7, width=14, fill_color=BLUE_E, fill_opacity=0.3)
        sky.move_to(UP * 0.5)
        
        self.play(FadeIn(ground), FadeIn(sky))
        
        # 太阳
        # 创建太阳主体
        sun_core = Circle(radius=0.5, fill_color=YELLOW, fill_opacity=1)
        
        # 创建光晕效果（多层半透明光环）
        glow_layers = VGroup()
        num_layers = 4
        max_radius = 0.9
        for i in range(num_layers):
            opacity = 0.7 * (1 - i / num_layers)  # 外层透明度逐渐降低
            radius = 0.5 + (i+1) * (max_radius - 0.5) / num_layers
            glow = Circle(radius=radius, stroke_opacity=0, fill_color=YELLOW, fill_opacity=opacity)
            glow_layers.add(glow)
        
        # 创建光花（尖光效果）
        rays = VGroup()
        num_rays = 12
        ray_length = 0.8
        for i in range(num_rays):
            angle = i * TAU / num_rays
            start_point = 0.5 * np.array([np.cos(angle), np.sin(angle), 0])
            end_point = (0.5 + ray_length) * np.array([np.cos(angle), np.sin(angle), 0])
            ray = Line(start_point, end_point, stroke_color=YELLOW, stroke_width=3, stroke_opacity=0.7)
            rays.add(ray)
        
        # 将所有元素组合成太阳
        sun = VGroup(glow_layers, sun_core, rays)
        sun.move_to(UP * 2 + RIGHT * 5)
        
        # 添加脉动效果
        self.play(FadeIn(sun))
        
        # 创建脉动动画
        self.play(
            *[glow.animate.scale(1.1).set_opacity(glow.get_fill_opacity() * 0.8) for glow in glow_layers],
            rate_func=there_and_back,
            run_time=1.5
        )
        
        # 添加持续的脉动效果
        sun_pulsing = [
            Succession(
                ApplyMethod(glow.scale, 1.1, rate_func=there_and_back, run_time=2),
                ApplyMethod(glow.scale, 1/1.1, rate_func=there_and_back, run_time=0)
            )
            for glow in glow_layers
        ]
        self.play(*sun_pulsing)
        
        # 地面加热和空气上升
        heat_rays = VGroup()
        for i in range(10):
            x_pos = np.random.uniform(-6, 6)
            start_point = np.array([x_pos, -2.5, 0])
            end_point = np.array([x_pos, -1.5, 0])
            ray = Line(start_point, end_point, color=RED)
            heat_rays.add(ray)
        
        # 添加说明文字
        heat_text = Text("太阳加热地面", font="SimSun", color=RED).scale(0.6)
        # 将文字放在热射线的上方而不是右侧，避免超出右边界
        heat_text.next_to(heat_rays, UP, buff=0.5).shift(LEFT * 2)
        
        # 同时显示热射线和说明文字
        self.play(Create(heat_rays), Write(heat_text))
        
        # 显示一段时间后文字消失
        self.wait(1.5)
        self.play(FadeOut(heat_text))
        
        # 温暖潮湿空气上升
        air_particles = VGroup()
        for i in range(20):
            x_pos = np.random.uniform(-5, 5)
            y_pos = np.random.uniform(-2, -1)
            particle = Dot(point=np.array([x_pos, y_pos, 0]), color=WHITE)
            air_particles.add(particle)
            
        # 添加说明文字
        air_text = Text("温暖潮湿空气上升", font="SimSun", color=WHITE).scale(0.6)
        # 将文字放在空气粒子的上方而不是右侧，避免超出右边界
        air_text.next_to(air_particles, UP, buff=0.5).shift(LEFT * 2)
        
        # 显示空气粒子和说明文字
        self.add(air_particles)
        self.play(Write(air_text))
        
        # 空气上升路径
        def air_path(mob, dt):
            mob.shift(UP * dt * 0.5)
            # 如果到达云层高度，重置到底部
            if mob.get_center()[1] > 2:
                mob.move_to(np.array([np.random.uniform(-5, 5), -2, 0]))
        
        # 添加空气粒子的运动更新器
        air_particles.add_updater(air_path)
        
        # 显示一段时间后文字消失
        self.wait(1.5)
        self.play(FadeOut(air_text))
        
        # 继续运动一段时间
        self.wait(1.5)
        air_particles.remove_updater(air_path)
        
        # 形成积雷云
        cloud_base = Polygon(
            np.array([-5, 0, 0]),
            np.array([5, 0, 0]),
            np.array([4, 1, 0]),
            np.array([-4, 1, 0]),
            fill_color=LIGHT_GREY, fill_opacity=0.8, stroke_width=0
        )
        
        cloud_top = Polygon(
            np.array([-4, 1, 0]),
            np.array([4, 1, 0]),
            np.array([2, 3, 0]),
            np.array([-2, 3, 0]),
            fill_color=DARK_GREY, fill_opacity=0.9, stroke_width=0
        )
        
        cloud = VGroup(cloud_base, cloud_top)
        
        # 添加说明文字
        cloud_text = Text("形成雷雨云", font="SimSun", color=LIGHT_GREY).scale(0.6)
        # 将文字放在云的上方而不是右侧，避免超出右边界
        cloud_text.next_to(cloud, UP, buff=0.5).shift(LEFT * 2)
        
        # 同时显示云和说明文字
        self.play(FadeIn(cloud), Write(cloud_text))
        
        # 显示一段时间后文字消失
        self.wait(1.5)
        self.play(FadeOut(cloud_text))
        
        # 显示温度分布
        zero_degree_line = DashedLine(
            np.array([-6, 1, 0]),
            np.array([6, 1, 0]),
            color=BLUE
        )
        
        # 添加温度和高度信息
        zero_degree_text = Text("0°C (约高5000米)", font="SimSun", color=BLUE).scale(0.5)
        # 将文本放在虚线的上方
        zero_degree_text.next_to(zero_degree_line.get_start(), UP, buff=0.3)
        # 向右偏移一点，避免贴近屏幕边缘
        zero_degree_text.shift(RIGHT * 0.5)
        
        minus_forty_line = DashedLine(
            np.array([-6, 2.5, 0]),
            np.array([6, 2.5, 0]),
            color=BLUE_E
        )
        
        # 添加温度和高度信息
        minus_forty_text = Text("-40°C (约高10000米)", font="SimSun", color=BLUE_E).scale(0.5)
        # 将文本放在虚线的上方
        minus_forty_text.next_to(minus_forty_line.get_start(), UP, buff=0.3)
        # 向右偏移一点，避免贴近屏幕边缘
        minus_forty_text.shift(RIGHT * 0.5)
        
        self.play(
            Create(zero_degree_line),
            Write(zero_degree_text),
            Create(minus_forty_line),
            Write(minus_forty_text)
        )
        
        self.wait(2)
        
        # 清除场景准备下一部分
        self.play(
            FadeOut(subtitle),
            FadeOut(heat_rays),
            FadeOut(air_particles)
        )
        
        # 保留云、温度线和地面/天空供下一场景使用
        self.cloud = cloud
        self.zero_degree_line = zero_degree_line
        self.zero_degree_text = zero_degree_text
        self.minus_forty_line = minus_forty_line
        self.minus_forty_text = minus_forty_text
        self.ground = ground
        self.sky = sky
        self.sun = sun
    
    def initial_hailstone_formation(self):
        # 更新标题
        subtitle = Text("冰雹形成的初始阶段", font="SimSun", color=BLUE).scale(0.8)
        subtitle.to_edge(UP)
        self.play(ReplacementTransform(self.main_title, subtitle))
        
        # 创建水滴
        water_drops = VGroup()
        for i in range(15):
            x_pos = np.random.uniform(-3, 3)
            y_pos = np.random.uniform(0.2, 0.8)
            drop = Circle(radius=0.05, fill_color=BLUE, fill_opacity=0.8, stroke_width=0)
            drop.move_to(np.array([x_pos, y_pos, 0]))
            water_drops.add(drop)
        
        self.play(FadeIn(water_drops))
        
        # 水滴上升到冷区
        self.play(water_drops.animate.shift(UP * 1.5), run_time=2)
        
        # 水滴冻结成冰晶
        ice_crystals = VGroup()
        for drop in water_drops:
            pos = drop.get_center()
            crystal = Star(n=6, outer_radius=0.08, inner_radius=0.05, 
                          fill_color=WHITE, fill_opacity=1, stroke_width=0)
            crystal.move_to(pos)
            ice_crystals.add(crystal)
        
        self.play(
            FadeOut(water_drops),
            FadeIn(ice_crystals)
        )
        
        # 文字说明
        explanation = Text(
            "在0°C以上，水以液态存在\n"
            "上升到0°C以下，水滴开始冻结\n"
            "在-40°C附近，水滴迅速冻结成冰晶",
            font="SimSun", color=WHITE
        ).scale(0.5)
        explanation.to_edge(RIGHT, buff=0.5)
        
        self.play(Write(explanation))
        self.wait(2)
        
        # 清除场景准备下一部分
        self.play(
            FadeOut(subtitle),
            FadeOut(explanation)
        )
        
        # 保留冰晶供下一场景使用
        self.ice_crystals = ice_crystals
    
    def hailstone_growth(self):
        # 更新标题
        subtitle = Text("冰雹的生长过程", font="SimSun", color=BLUE).scale(0.8)
        subtitle.to_edge(UP)
        
        # 先淡出上一个标题，再显示新标题
        self.play(FadeOut(self.main_title), FadeIn(subtitle))
        
        # 选择一个冰晶作为主要观察对象
        main_crystal = self.ice_crystals[7].copy()
        
        # 放大查看这个冰晶
        self.play(
            FadeOut(self.ice_crystals),
            main_crystal.animate.move_to(ORIGIN).scale(3)
        )
        
        # 创建上升气流箭头
        updraft_arrows = VGroup()
        for i in range(5):
            x_pos = np.random.uniform(-3, 3)
            arrow = Arrow(
                np.array([x_pos, -2, 0]),
                np.array([x_pos, 2, 0]),
                color=WHITE, buff=0
            )
            updraft_arrows.add(arrow)
        
        # 添加上升气流文字说明
        updraft_text = Text("强烈的上升气流", font="SimSun", color=WHITE).scale(0.6)
        # 将文字放在箭头的上方而不是右侧，避免超出右边界
        updraft_text.next_to(updraft_arrows, UP, buff=0.5).shift(LEFT * 2)
        
        self.play(Create(updraft_arrows), Write(updraft_text))
        
        # 显示一段时间后文字消失
        self.wait(1.5)
        self.play(FadeOut(updraft_text))
        
        # 添加温度线的参考位置（不显示，只用于定位）
        zero_degree_y = 1  # 0°C温度线的y坐标
        
        # 冰雹上下循环的路径，跨越0°C温度线
        cycle_path = CubicBezier(
            np.array([0, 0, 0]),  # 起点
            np.array([1, zero_degree_y + 2, 0]),  # 控制点1，在0°C以上
            np.array([-1, zero_degree_y - 1, 0]),  # 控制点2，在0°C以下
            np.array([0, 0, 0])  # 终点回到原位置
        )
   
        
        # 创建过冷水滴
        supercooled_drops = VGroup()
        for i in range(20):
            x_pos = np.random.uniform(-2, 2)
            y_pos = np.random.uniform(-1.5, 1.5)
            drop = Circle(radius=0.05, fill_color=BLUE_A, fill_opacity=0.8, stroke_width=0)
            drop.move_to(np.array([x_pos, y_pos, 0]))
            supercooled_drops.add(drop)
        
        self.play(FadeIn(supercooled_drops))
        
        # 冰雹生长过程 - 第一次循环
        self.play(MoveAlongPath(main_crystal, cycle_path), run_time=2)
        
        # 冰雹变大
        bigger_hailstone = Circle(radius=0.3, fill_color=WHITE, fill_opacity=1, stroke_color=BLUE_E, stroke_width=1)
        bigger_hailstone.move_to(main_crystal.get_center())
        
        self.play(
            FadeOut(main_crystal),
            FadeIn(bigger_hailstone)
        )
        
        # 文字说明
        explanation = Text(
            "冰晶在强上升气流中上下循环\n"
            "与过冷水滴碰撞后冻结在表面\n"
            "形成同心圆层状结构",
            font="SimSun", color=WHITE
        ).scale(0.5)
        explanation.to_edge(RIGHT, buff=0.5)
        
        self.play(Write(explanation))
        
        # 冰雹生长过程 - 第二次循环，更明显地跨越0°C温度线
        cycle_path2 = CubicBezier(
            bigger_hailstone.get_center(),  # 起点
            np.array([1.5, zero_degree_y + 3, 0]),  # 控制点1，更高地进入冻结区
            np.array([-1.5, zero_degree_y - 0.7, 0]),  # 控制点2，更低地进入融化区
            np.array([0, 0, 0])  # 终点回到中心位置
        )
        
        self.play(MoveAlongPath(bigger_hailstone, cycle_path2), run_time=2)
        
        # 冰雹继续变大
        even_bigger_hailstone = Circle(radius=0.5, fill_color=WHITE, fill_opacity=1, stroke_color=BLUE_E, stroke_width=1)
        even_bigger_hailstone.move_to(bigger_hailstone.get_center())
        
        # 添加层状结构
        inner_layer = Circle(radius=0.3, stroke_color=LIGHT_GREY, stroke_width=1, fill_opacity=0)
        inner_layer.move_to(even_bigger_hailstone.get_center())
        
        self.play(
            FadeOut(bigger_hailstone),
            FadeIn(even_bigger_hailstone),
            FadeIn(inner_layer)
        )
        
        # 展示冰雹内部结构
        hailstone_cross_section = VGroup()
        
        # 保存当前场景元素，以便后续恢复
        current_mobjects = self.mobjects.copy()
        
        # 创建小冰雹截面（与当前冰雹大小相同）
        small_cross_section = Circle(radius=0.5, fill_opacity=0)
        small_cross_section.set_fill(WHITE, opacity=0.2)  # 核心
        
        # 添加小冰雹的中间层
        small_middle_layer = Annulus(inner_radius=0.2, outer_radius=0.35, fill_opacity=0.5, fill_color=LIGHT_GREY, stroke_width=0)
        
        # 添加小冰雹的外层
        small_outer_layer = Annulus(inner_radius=0.35, outer_radius=0.5, fill_opacity=0.3, fill_color=BLUE_A, stroke_width=0)
        
        small_cross_section.add(small_middle_layer, small_outer_layer)
        small_cross_section.move_to(even_bigger_hailstone.get_center())  # 与当前冰雹位置相同
        
        # 先显示小冰雹截面
        self.play(FadeIn(small_cross_section))
        
        # 完全清除原场景，只保留标题和小冰雹截面
        for mob in list(self.mobjects):
            if mob != self.main_title and mob != small_cross_section:
                self.remove(mob)
        
        # 创建更大的冰雹截面
        hailstone_cross_section = Circle(radius=1.5, fill_opacity=0)
        hailstone_cross_section.set_fill(WHITE, opacity=0.2)  # 核心
        
        # 添加中间层
        middle_layer = Annulus(inner_radius=0.6, outer_radius=1.05, fill_opacity=0.5, fill_color=LIGHT_GREY, stroke_width=0)
        
        # 添加外层
        outer_layer = Annulus(inner_radius=1.05, outer_radius=1.5, fill_opacity=0.3, fill_color=BLUE_A, stroke_width=0)
        
        # 将小冰雹截面放大到大冰雹截面
        hailstone_cross_section.add(middle_layer, outer_layer)
        # 将冰雹截面往左移动，给右侧文字留出更多空间
        hailstone_cross_section.move_to(LEFT * 2)
        
        # 将小冰雹截面放大到大冰雹截面
        self.play(Transform(small_cross_section, hailstone_cross_section))
        
        # 移除小冰雹截面，使用大冰雹截面
        self.remove(small_cross_section)
        self.add(hailstone_cross_section)
        
        # 添加标题，更大更显眼
        cross_section_title = Text("冰雹的内部结构", font="SimSun", color=BLUE).scale(0.8)
        cross_section_title.to_edge(UP, buff=0.5)
        
        # 显示标题
        self.play(Write(cross_section_title))
        
        # 标注不同层的形成，使用更大的字体
        layer_labels = VGroup()
        
        # 调整标签位置，使其更整齐
        outer_label = Text("透明冰层\n(快速冻结)", font="SimSun", color=BLUE_A).scale(0.6)  # 增大字体并使用相应颜色
        outer_label.next_to(hailstone_cross_section, RIGHT, buff=1.0).shift(UP * 1.5)  # 增加间距
        
        middle_label = Text("乳白色冰层\n(含有气泡)", font="SimSun", color=LIGHT_GREY).scale(0.6)  # 增大字体并使用相应颜色
        middle_label.next_to(hailstone_cross_section, RIGHT, buff=1.0)  # 增加间距
        
        core_label = Text("冰晶核心", font="SimSun", color=WHITE).scale(0.6)  # 增大字体
        core_label.next_to(hailstone_cross_section, RIGHT, buff=1.0).shift(DOWN * 1.5)  # 增加间距
        
        # 计算冰雹各层的准确位置，考虑到冰雹已经向左移动
        center = LEFT * 2  # 冰雹中心位置
        
        # 外层箭头 - 从透明冰层开始
        # 外层边界位置：半径 1.5，与中心的偏移量为 (1.3, 0.7, 0)
        outer_layer_point = center + np.array([1.1, 0.7, 0])  # 外层右上方位置
        outer_line = Arrow(
            outer_layer_point, 
            outer_label.get_left() - np.array([0.2, 0, 0]), 
            color=BLUE_A,
            buff=0.1,
            stroke_width=3  # 增加箭头线宽
        )
        
        # 中间层箭头 - 从乳白色冰层开始
        # 中间层位置：半径约 0.8，与中心的偏移量为 (0.7, 0, 0)
        middle_layer_point = center + np.array([0.7, 0, 0])  # 中间层右侧位置
        middle_line = Arrow(
            middle_layer_point, 
            middle_label.get_left() - np.array([0.2, 0, 0]), 
            color=LIGHT_GREY,
            buff=0.1,
            stroke_width=3  # 增加箭头线宽
        )
        
        # 核心箭头 - 从冰晶核心开始
        # 核心位置：半径约 0.3，与中心的偏移量为 (0.2, -0.1, 0)
        core_layer_point = center + np.array([0.2, -0.1, 0])  # 核心右下方位置
        core_line = Arrow(
            core_layer_point, 
            core_label.get_left() - np.array([0.2, 0, 0]), 
            color=WHITE,
            buff=0.1,
            stroke_width=3  # 增加箭头线宽
        )
        
        # 将标签和连接线都添加到组中
        layer_labels.add(outer_label, middle_label, core_label, outer_line, middle_line, core_line)
        
        # 添加每层的温度和成因说明，增大字体
        layer_info = VGroup()
        
        outer_info = Text("温度：-10°C左右\n快速冻结形成透明冰", font="SimSun", color=BLUE_A).scale(0.5)  # 增大字体
        outer_info.next_to(outer_label, RIGHT, buff=0.5)
        
        middle_info = Text("温度：-15°C至-25°C\n缓慢冻结捕获气泡", font="SimSun", color=LIGHT_GREY).scale(0.5)  # 增大字体
        middle_info.next_to(middle_label, RIGHT, buff=0.5)
        
        core_info = Text("温度：-30°C左右\n由冰晶或冰冻的水滴形成", font="SimSun", color=WHITE).scale(0.5)  # 增大字体
        core_info.next_to(core_label, RIGHT, buff=0.5)
        
        layer_info.add(outer_info, middle_info, core_info)
        
        # 先显示标签和线条，然后显示详细信息
        self.play(Write(layer_labels), run_time=1.5)
        self.play(Write(layer_info), run_time=1.5)
        
        # 给观众足够时间查看详细结构
        self.wait(3)
        
        # 创建小冰雹截面（用于缩小效果）
        small_cross_section_end = Circle(radius=0.5, fill_opacity=0)
        small_cross_section_end.set_fill(WHITE, opacity=0.2)
        
        # 添加小冰雹的中间层和外层
        small_middle_layer_end = Annulus(inner_radius=0.2, outer_radius=0.35, fill_opacity=0.5, fill_color=LIGHT_GREY, stroke_width=0)
        small_outer_layer_end = Annulus(inner_radius=0.35, outer_radius=0.5, fill_opacity=0.3, fill_color=BLUE_A, stroke_width=0)
        
        small_cross_section_end.add(small_middle_layer_end, small_outer_layer_end)
        small_cross_section_end.move_to(even_bigger_hailstone.get_center())  # 与原冰雹位置相同
        
        # 先淡出标签和信息
        self.play(
            FadeOut(cross_section_title),
            FadeOut(layer_labels),
            FadeOut(layer_info)
        )
        
        # 然后将大冰雹截面缩小回小冰雹截面
        self.play(Transform(hailstone_cross_section, small_cross_section_end))
        
        # 恢复原来的场景（除了main_title外的所有元素）
        restored_mobjects = [mob for mob in current_mobjects if mob != self.main_title]
        
        # 淡出缩小后的冰雹截面，同时淡入原来的场景
        self.play(
            FadeOut(hailstone_cross_section),
            FadeIn(*restored_mobjects)
        )
        
        self.wait(2)
        
        # 清除场景准备下一部分
        self.play(
            FadeOut(subtitle),
            FadeOut(explanation),
            FadeOut(updraft_arrows),
            FadeOut(supercooled_drops),
            FadeOut(inner_layer)
        )
        
        # 保留最终的冰雹供下一场景使用
        self.final_hailstone = even_bigger_hailstone
    
    def hailstone_size_factors(self):
        # 更新标题
        subtitle = Text("冰雹大小的决定因素", font="SimSun", color=BLUE).scale(0.8)
        subtitle.to_edge(UP)
        
        # 先淡出上一个标题，再显示新标题
        self.play(FadeOut(self.main_title), FadeIn(subtitle))
        
        # 创建背景元素：云层和温度线
        cloud_copy = self.cloud.copy()
        zero_degree_line_copy = self.zero_degree_line.copy()
        zero_degree_text_copy = self.zero_degree_text.copy()
        
        self.play(
            FadeIn(cloud_copy),
            FadeIn(zero_degree_line_copy),
            FadeIn(zero_degree_text_copy)
        )
        
        # 创建强上升气流区域
        strong_updraft_area = Rectangle(
            height=4, width=3, 
            fill_color=RED, fill_opacity=0.2,
            stroke_opacity=0
        )
        strong_updraft_area.move_to(np.array([-3, 0, 0]))
        
        # 创建弱上升气流区域
        weak_updraft_area = Rectangle(
            height=4, width=3, 
            fill_color=BLUE, fill_opacity=0.2,
            stroke_opacity=0
        )
        weak_updraft_area.move_to(np.array([3, 0, 0]))
        
        # 添加区域标签
        strong_area_label = Text("强上升气流区", font="SimSun", color=RED).scale(0.6)
        strong_area_label.move_to(strong_updraft_area.get_top()).shift(DOWN * 0.5)
        
        weak_area_label = Text("弱上升气流区", font="SimSun", color=BLUE).scale(0.6)
        weak_area_label.move_to(weak_updraft_area.get_top()).shift(DOWN * 0.5)
        
        self.play(
            FadeIn(strong_updraft_area), Write(strong_area_label),
            FadeIn(weak_updraft_area), Write(weak_area_label)
        )
        
        # 创建强上升气流箭头
        strong_updrafts = VGroup()
        for i in range(5):
            x_pos = np.random.uniform(-4, -2)
            start_y = -2
            arrow = Arrow(
                np.array([x_pos, start_y, 0]),
                np.array([x_pos, start_y + 1.5, 0]),
                color=RED, buff=0, stroke_width=3
            )
            strong_updrafts.add(arrow)
        
        # 创建弱上升气流箭头
        weak_updrafts = VGroup()
        for i in range(5):
            x_pos = np.random.uniform(2, 4)
            start_y = -2
            arrow = Arrow(
                np.array([x_pos, start_y, 0]),
                np.array([x_pos, start_y + 0.8, 0]),
                color=BLUE, buff=0, stroke_width=1.5
            )
            weak_updrafts.add(arrow)
        
        # 显示上升气流箭头
        self.play(
            LaggedStart(*[GrowArrow(arrow) for arrow in strong_updrafts]),
            LaggedStart(*[GrowArrow(arrow) for arrow in weak_updrafts]),
            run_time=2
        )
        
        # 创建上升气流的动画函数
        def update_strong_updrafts(arrows, dt):
            for arrow in arrows:
                arrow.shift(UP * dt * 2)  # 强上升气流移动更快
                if arrow.get_center()[1] > 2:
                    arrow.move_to(np.array([arrow.get_center()[0], -2, 0]))
        
        def update_weak_updrafts(arrows, dt):
            for arrow in arrows:
                arrow.shift(UP * dt * 0.8)  # 弱上升气流移动更慢
                if arrow.get_center()[1] > 2:
                    arrow.move_to(np.array([arrow.get_center()[0], -2, 0]))
        
        # 添加更新器
        strong_updrafts.add_updater(update_strong_updrafts)
        weak_updrafts.add_updater(update_weak_updrafts)
        
        # 显示一段时间的动画
        self.wait(3)
        
        # 创建冰雹
        large_hail = Circle(radius=0.4, fill_color=WHITE, fill_opacity=1, stroke_color=BLUE_E, stroke_width=1)
        large_hail.move_to(np.array([-3, 0, 0]))
        
        small_hail = Circle(radius=0.2, fill_color=WHITE, fill_opacity=1, stroke_color=BLUE_E, stroke_width=1)
        small_hail.move_to(np.array([3, 0, 0]))
        
        # 添加冰雹标签
        large_hail_label = Text("大冰雹", font="SimSun", color=WHITE).scale(0.5)
        large_hail_label.next_to(large_hail, RIGHT, buff=0.3)
        
        small_hail_label = Text("小冰雹", font="SimSun", color=WHITE).scale(0.5)
        small_hail_label.next_to(small_hail, RIGHT, buff=0.3)
        
        self.play(
            FadeIn(large_hail), Write(large_hail_label),
            FadeIn(small_hail), Write(small_hail_label)
        )
        
        # 添加冰雹上下运动的动画
        # 大冰雹在强上升气流中上下循环
        large_hail_path = CubicBezier(
            large_hail.get_center(),  # 起点
            np.array([-3.5, 1.5, 0]),  # 控制点1，在冻结区
            np.array([-2.5, 0.5, 0]),  # 控制点2，在融化区
            large_hail.get_center()  # 终点
        )
        
        # 小冰雹在弱上升气流中上下循环，但很快就会下降
        small_hail_path = CubicBezier(
            small_hail.get_center(),  # 起点
            np.array([2.8, 0.8, 0]),  # 控制点1，不能升得太高
            np.array([3.2, 0.3, 0]),  # 控制点2
            np.array([3, -1, 0])  # 终点，下降
        )
        
        # 同时运行两个冰雹的动画
        self.play(
            MoveAlongPath(large_hail, large_hail_path),
            MoveAlongPath(small_hail, small_hail_path),
            run_time=3
        )
        
        # 大冰雹继续循环，小冰雹下降
        large_hail_path2 = CubicBezier(
            large_hail.get_center(),  # 起点
            np.array([-3.5, 1.5, 0]),  # 控制点1
            np.array([-2.5, 0.5, 0]),  # 控制点2
            large_hail.get_center()  # 终点
        )
        
        small_hail_falling = small_hail.animate.move_to(np.array([3, -3, 0]))
        
        self.play(
            MoveAlongPath(large_hail, large_hail_path2),
            small_hail_falling,
            run_time=2
        )
        
        # 添加文字说明
        explanation = Text(
            "强上升气流可以支撑更大的冰雹在空中循环更长时间\n"
            "使其收集更多水滴并不断增大\n"
            "弱上升气流只能支撑小冰雹，很快就会使冰雹降落",
            font="SimSun", color=WHITE
        ).scale(0.5)
        explanation.to_edge(DOWN, buff=0.5)
        
        self.play(Write(explanation))
        
        # 移除更新器
        strong_updrafts.remove_updater(update_strong_updrafts)
        weak_updrafts.remove_updater(update_weak_updrafts)
        
        # 显示一段时间后清除
        self.wait(3)
        self.play(
            FadeOut(cloud_copy), FadeOut(zero_degree_line_copy), FadeOut(zero_degree_text_copy),
            FadeOut(strong_updraft_area), FadeOut(weak_updraft_area),
            FadeOut(strong_area_label), FadeOut(weak_area_label),
            FadeOut(strong_updrafts), FadeOut(weak_updrafts),
            FadeOut(large_hail), FadeOut(large_hail_label),
            FadeOut(small_hail), FadeOut(small_hail_label),
            FadeOut(explanation),
            FadeOut(subtitle)
        )
    
    def hailstone_falling(self):
        # 更新标题
        subtitle = Text("冰雹的最终形成和降落", font="SimSun", color=BLUE).scale(0.8)
        subtitle.to_edge(UP)
        
        # 先淡出上一个标题，再显示新标题
        self.play(FadeOut(self.main_title), FadeIn(subtitle))
        
        # 创建多个不同大小的冰雹
        hailstones = VGroup()
        
        # 复制最终冰雹并创建不同大小的冰雹
        for i in range(5):
            size = np.random.uniform(0.2, 0.6)
            hailstone = Circle(radius=size, fill_color=WHITE, fill_opacity=1, stroke_color=BLUE_E, stroke_width=1)
            x_pos = np.random.uniform(-4, 4)
            y_pos = np.random.uniform(1, 2)
            hailstone.move_to(np.array([x_pos, y_pos, 0]))
            hailstones.add(hailstone)
        
        # 添加最终冰雹到组
        self.final_hailstone.move_to(np.array([0, 1.5, 0]))
        hailstones.add(self.final_hailstone)
        
        self.play(FadeIn(hailstones))
        
        # 文字说明
        explanation = Text(
            "当冰雹质量超过上升气流的支撑力\n"
            "它们开始从云中降落\n"
            "大小从花生到高尔夫球甚至更大",
            font="SimSun", color=WHITE
        ).scale(0.5)
        explanation.to_edge(RIGHT, buff=0.5)
        
        self.play(Write(explanation))
        
        # 冰雹降落动画
        self.play(hailstones.animate.shift(DOWN * 4), run_time=2)
        
        # 冰雹落地效果
        impact_effects = VGroup()
        for hailstone in hailstones:
            pos = hailstone.get_center()
            impact = Star(n=8, outer_radius=0.2, inner_radius=0.1,
                         fill_color=WHITE, fill_opacity=0.8, stroke_width=0)
            impact.move_to(np.array([pos[0], -2.9, 0]))  # 地面位置
            impact_effects.add(impact)
        
        self.play(
            FadeIn(impact_effects),
            FadeOut(hailstones)
        )
        
        self.play(FadeOut(impact_effects))
        
        # 展示不同大小的冰雹比较
        hail_sizes = VGroup()
        
        small_hail = Circle(radius=0.2, fill_color=WHITE, fill_opacity=1, stroke_color=GREY, stroke_width=1)
        small_hail.move_to(LEFT * 3)
        small_label = Text("小型冰雹\n(直径<1cm)", font="SimSun", color=WHITE).scale(0.4)
        small_label.next_to(small_hail, DOWN)
        
        medium_hail = Circle(radius=0.4, fill_color=WHITE, fill_opacity=1, stroke_color=GREY, stroke_width=1)
        medium_hail.move_to(ORIGIN)
        medium_label = Text("中型冰雹\n(直径1-3cm)", font="SimSun", color=WHITE).scale(0.4)
        medium_label.next_to(medium_hail, DOWN)
        
        large_hail = Circle(radius=0.6, fill_color=WHITE, fill_opacity=1, stroke_color=GREY, stroke_width=1)
        large_hail.move_to(RIGHT * 3)
        large_label = Text("大型冰雹\n(直径>3cm)", font="SimSun", color=WHITE).scale(0.4)
        large_label.next_to(large_hail, DOWN)
        
        hail_sizes.add(small_hail, small_label, medium_hail, medium_label, large_hail, large_label)
        
        self.play(FadeIn(hail_sizes))
        
        self.wait(2)
        
        # 清除场景准备下一部分
        self.play(
            FadeOut(subtitle),
            FadeOut(explanation),
            FadeOut(hail_sizes),
            FadeOut(self.cloud),
            FadeOut(self.zero_degree_line),
            FadeOut(self.zero_degree_text),
            FadeOut(self.minus_forty_line),
            FadeOut(self.minus_forty_text),
            FadeOut(self.ground),
            FadeOut(self.sky),
            FadeOut(self.sun)
        )
    
    def summary(self):
        # 更新标题
        subtitle = Text("科学解释和总结", font="SimSun", color=BLUE).scale(0.8)
        subtitle.to_edge(UP)
        
        # 先淡出上一个标题，再显示新标题
        self.play(FadeOut(self.main_title), FadeIn(subtitle))
        
        # 冰雹形成的关键条件
        conditions = VGroup()
        
        condition1 = Text("1. 强烈的上升气流", font="SimSun", color=WHITE).scale(0.6)
        condition1.to_edge(LEFT, buff=1).shift(UP * 1)
        
        condition2 = Text("2. 足够的过冷水滴", font="SimSun", color=WHITE).scale(0.6)
        condition2.next_to(condition1, DOWN, aligned_edge=LEFT)
        
        condition3 = Text("3. 温度分层（0°C到-40°C）", font="SimSun", color=WHITE).scale(0.6)
        condition3.next_to(condition2, DOWN, aligned_edge=LEFT)
        
        condition4 = Text("4. 足够长的悬浮时间", font="SimSun", color=WHITE).scale(0.6)
        condition4.next_to(condition3, DOWN, aligned_edge=LEFT)
        
        conditions.add(condition1, condition2, condition3, condition4)
        
        self.play(Write(conditions), run_time=2)
        
        # 冰雹大小与雷雨云强度的关系
        intensity_chart = VGroup()
        
        # 坐标轴
        x_axis = Line(np.array([-3, -1, 0]), np.array([3, -1, 0]))
        y_axis = Line(np.array([-3, -1, 0]), np.array([-3, 2, 0]))
        
        # 轴标签
        x_label = Text("雷雨云强度", font="SimSun", color=WHITE).scale(0.4)
        x_label.next_to(x_axis, DOWN)
        
        y_label = Text("冰雹直径", font="SimSun", color=WHITE).scale(0.4)
        y_label.next_to(y_axis, LEFT)
        
        # 关系曲线 - 表示雷雨云强度越大，冰雹直径越大
        # 先创建坐标系
        axes = Axes(
            x_range=[0, 6, 1],
            y_range=[0, 3, 1],
            axis_config={"color": WHITE},
            x_length=6,
            y_length=3
        )
        axes.shift(np.array([0, 0, 0]))
        
        # 在坐标系上创建曲线
        relation_curve = axes.plot(
            lambda x: 0.4 * x,  # 线性增长函数，表示正相关
            x_range=[0, 6],  # 从原点开始
            color=RED
        )
        
        # 添加坐标轴标签
        x_label = Text("雷雨云强度", font="SimSun", color=WHITE).scale(0.4)
        x_label.next_to(axes.x_axis, DOWN)
        
        y_label = Text("冰雹直径", font="SimSun", color=WHITE).scale(0.4)
        y_label.next_to(axes.y_axis, LEFT)
        
        # 移动整个图表到右侧
        chart_group = VGroup(axes, relation_curve, x_label, y_label)
        chart_group.shift(RIGHT * 3)
        
        intensity_chart.add(chart_group)
        
        self.play(Create(intensity_chart), run_time=2)
        
        self.wait(2)

        # 淡出之前的内容，为新内容腾出空间
        self.play(
            FadeOut(conditions),
            run_time=1
        )
        
        
        # 冰雹的危害和防护措施
        hazards_title = Text("冰雹的危害和防护", font="SimSun", color=YELLOW).scale(0.6)
        # 放在屏幕上方，不需要向下移动那么多，因为已经清空了空间
        hazards_title.to_edge(LEFT, buff=1).shift(UP * 1)
        
        hazards = VGroup()
        
        hazard1 = Text("• 农作物损失", font="SimSun", color=WHITE).scale(0.5)
        hazard1.next_to(hazards_title, DOWN, aligned_edge=LEFT)
        
        hazard2 = Text("• 车辆和建筑物损坏", font="SimSun", color=WHITE).scale(0.5)
        hazard2.next_to(hazard1, DOWN, aligned_edge=LEFT)
        
        # 将防护措施分成多行，更加清晰
        protection_title = Text("防护措施：", font="SimSun", color=GREEN).scale(0.5)
        protection_title.next_to(hazard2, DOWN, aligned_edge=LEFT)
        
        protection1 = Text("• 提前预警", font="SimSun", color=GREEN).scale(0.5)
        protection1.next_to(protection_title, DOWN, aligned_edge=LEFT)
        
        protection2 = Text("• 寻找遮蔽", font="SimSun", color=GREEN).scale(0.5)
        protection2.next_to(protection1, DOWN, aligned_edge=LEFT)
        
        protection3 = Text("• 使用防护网", font="SimSun", color=GREEN).scale(0.5)
        protection3.next_to(protection2, DOWN, aligned_edge=LEFT)
        
        # 先添加标题和危害
        hazards.add(hazard1, hazard2)
        
        # 先显示标题
        self.play(Write(hazards_title), run_time=1)
        
        # 再显示危害
        self.play(Write(hazards), run_time=1.5)
        
        # 创建防护措施组
        protections = VGroup(protection_title, protection1, protection2, protection3)
        
        # 最后显示防护措施
        self.play(Write(protections), run_time=1.5)
        
        self.wait(3)
        
        # 结束标题
        final_title = Text("冰雹的形成原理", font="SimSun", color=BLUE).scale(1.2)
        final_thanks = Text("谢谢观看", font="SimSun", color=WHITE).scale(0.8)
        final_thanks.next_to(final_title, DOWN)
        
        final_group = VGroup(final_title, final_thanks)
        
        self.play(
            FadeOut(subtitle),
            FadeOut(intensity_chart),
            FadeOut(hazards_title),
            FadeOut(hazards),
            FadeOut(protections)
        )
        
        self.play(FadeIn(final_group))
        self.wait(2)
        self.play(FadeOut(final_group))


# 运行动画
if __name__ == "__main__":
    scene = HailstoneFormation()
    scene.render()
